#!/usr/bin/env python3
"""
数据库错误修复脚本
解决 "no such table: plans" 错误
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_database_error():
    """修复数据库错误"""
    print("🔧 修复数据库错误...")
    print("=" * 50)
    
    # 1. 检查并安装必要的依赖
    print("📦 检查依赖...")
    
    required_packages = {
        "sqlalchemy": "sqlalchemy>=2.0.9",
        "bcrypt": "bcrypt>=4.0.1",
        "jwt": "PyJWT>=2.8.0",
        "email_validator": "email-validator>=2.0.0"
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            if package == "jwt":
                import jwt
            elif package == "email_validator":
                import email_validator
            else:
                __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n正在安装缺少的依赖: {', '.join(missing_packages)}")
        import subprocess
        
        for package in missing_packages:
            try:
                result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                      capture_output=True, text=True, check=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e.stderr}")
                return False
    
    # 2. 创建数据库表
    print("\n🗄️ 创建数据库表...")
    
    try:
        # 删除旧的数据库文件（如果存在且损坏）
        db_file = Path("business.db")
        if db_file.exists():
            print("🗑️ 删除旧的数据库文件...")
            db_file.unlink()
        
        # 导入并创建数据库表
        from auth_system import Base, engine, auth_service
        from billing_system import Plan, Subscription, Invoice, UsageQuota
        
        print("📋 创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
        # 3. 初始化默认数据
        print("\n📦 初始化默认套餐...")
        from billing_system import billing_service
        
        # 手动初始化默认套餐
        db = auth_service.get_db()
        try:
            # 检查是否已有套餐
            existing_plans = db.query(Plan).count()
            if existing_plans == 0:
                billing_service._init_default_plans()
                print("✅ 默认套餐初始化成功")
            else:
                print(f"✅ 已有 {existing_plans} 个套餐")
        finally:
            db.close()
        
        # 4. 创建示例用户
        print("\n👤 创建管理员用户...")
        from auth_system import UserCreate
        
        admin_data = UserCreate(
            email="<EMAIL>",
            username="admin",
            password="admin123456",
            full_name="系统管理员"
        )
        
        db = auth_service.get_db()
        try:
            # 检查用户是否已存在
            from auth_system import User
            existing_user = db.query(User).filter(User.email == admin_data.email).first()
            
            if not existing_user:
                user = auth_service.create_user(admin_data, db)
                print(f"✅ 管理员用户创建成功: {user.email}")
            else:
                print(f"✅ 管理员用户已存在: {existing_user.email}")
        except Exception as e:
            print(f"⚠️ 用户创建失败: {e}")
        finally:
            db.close()
        
        # 5. 验证数据库
        print("\n✅ 验证数据库...")
        db = auth_service.get_db()
        try:
            plans = billing_service.get_plans(db)
            users = db.query(auth_service.User).count()
            orgs = db.query(auth_service.Organization).count()
            
            print(f"📦 套餐数量: {len(plans)}")
            print(f"👥 用户数量: {users}")
            print(f"🏢 组织数量: {orgs}")
            
            if len(plans) > 0:
                print("\n📋 可用套餐:")
                for plan in plans:
                    print(f"  📦 {plan.name} - ¥{plan.monthly_price}/月")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        logger.error(f"Database fix failed: {e}")
        return False

def test_import():
    """测试导入功能"""
    print("\n🧪 测试导入功能...")
    
    try:
        # 测试认证系统
        from auth_system import auth_service
        print("✅ 认证系统导入成功")
        
        # 测试计费系统
        from billing_system import billing_service
        print("✅ 计费系统导入成功")
        
        # 测试仪表板系统
        from dashboard_system import dashboard_service
        print("✅ 仪表板系统导入成功")
        
        # 测试主应用
        print("🧪 测试主应用导入...")
        
        # 临时设置环境变量避免启动服务器
        os.environ["TESTING"] = "1"
        
        try:
            import app
            print("✅ 主应用导入成功")
        except Exception as e:
            print(f"⚠️ 主应用导入警告: {e}")
            # 这可能是正常的，因为某些依赖可能不可用
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 数据库错误修复完成!")
    print("\n📋 后续步骤:")
    print("1. 启动应用:")
    print("   python start_app.py")
    print("   或")
    print("   python app.py")
    print()
    print("2. 访问应用:")
    print("   主页: http://localhost:8000")
    print("   企业仪表板: http://localhost:8000/business")
    print("   AI记忆系统: http://localhost:8000/cognee")
    print()
    print("3. 管理员账户:")
    print("   邮箱: <EMAIL>")
    print("   密码: admin123456")
    print()
    print("💡 提示:")
    print("- 如果仍有问题，请运行: python init_database.py")
    print("- 生产环境请修改默认密码")
    print("- 可以通过企业仪表板管理用户和订阅")

def main():
    """主修复流程"""
    print("🔧 IntelliHub Pro - 数据库错误修复")
    print("=" * 50)
    
    # 1. 修复数据库错误
    if not fix_database_error():
        print("❌ 数据库修复失败")
        sys.exit(1)
    
    # 2. 测试导入
    if not test_import():
        print("⚠️ 导入测试失败，但数据库已修复")
    
    # 3. 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出修复")
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        logger.error(f"Fix failed: {e}")
        sys.exit(1)
