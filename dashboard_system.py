"""
企业级仪表板和分析系统
提供实时监控、使用分析、业务洞察
"""

import os
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging

from auth_system import SessionLocal, User, Organization, UsageRecord
from billing_system import Subscription, Plan, UsageQuota

logger = logging.getLogger(__name__)

# Pydantic模型
class DashboardStats(BaseModel):
    total_users: int
    active_users_today: int
    total_organizations: int
    total_api_calls_today: int
    total_api_calls_month: int
    revenue_month: float
    top_features: List[Dict[str, Any]]

class UserAnalytics(BaseModel):
    user_growth: List[Dict[str, Any]]
    user_activity: List[Dict[str, Any]]
    user_retention: Dict[str, float]
    geographic_distribution: List[Dict[str, Any]]

class UsageAnalytics(BaseModel):
    api_calls_trend: List[Dict[str, Any]]
    endpoint_usage: List[Dict[str, Any]]
    error_rates: List[Dict[str, Any]]
    response_times: List[Dict[str, Any]]

class RevenueAnalytics(BaseModel):
    revenue_trend: List[Dict[str, Any]]
    plan_distribution: List[Dict[str, Any]]
    churn_rate: float
    mrr: float  # Monthly Recurring Revenue
    arr: float  # Annual Recurring Revenue

class OrganizationInsights(BaseModel):
    id: int
    name: str
    plan: str
    users_count: int
    api_calls_month: int
    storage_used_gb: float
    last_activity: Optional[datetime]
    health_score: float

# 仪表板服务类
class DashboardService:
    def __init__(self):
        pass
    
    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
    
    def get_dashboard_stats(self, db: Session) -> DashboardStats:
        """获取仪表板统计数据"""
        today = datetime.utcnow().date()
        month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 总用户数
        total_users = db.query(User).count()
        
        # 今日活跃用户
        active_users_today = db.query(User).filter(
            func.date(User.last_login) == today
        ).count()
        
        # 总组织数
        total_organizations = db.query(Organization).count()
        
        # 今日API调用
        total_api_calls_today = db.query(UsageRecord).filter(
            func.date(UsageRecord.timestamp) == today
        ).count()
        
        # 本月API调用
        total_api_calls_month = db.query(UsageRecord).filter(
            UsageRecord.timestamp >= month_start
        ).count()
        
        # 本月收入
        revenue_month = db.query(func.sum(Subscription.amount)).filter(
            and_(
                Subscription.current_period_start >= month_start,
                Subscription.status == "active"
            )
        ).scalar() or 0
        
        # 热门功能
        top_features = self._get_top_features(db, month_start)
        
        return DashboardStats(
            total_users=total_users,
            active_users_today=active_users_today,
            total_organizations=total_organizations,
            total_api_calls_today=total_api_calls_today,
            total_api_calls_month=total_api_calls_month,
            revenue_month=float(revenue_month),
            top_features=top_features
        )
    
    def _get_top_features(self, db: Session, since: datetime) -> List[Dict[str, Any]]:
        """获取热门功能"""
        endpoint_stats = db.query(
            UsageRecord.endpoint,
            func.count(UsageRecord.id).label('count')
        ).filter(
            UsageRecord.timestamp >= since
        ).group_by(
            UsageRecord.endpoint
        ).order_by(
            func.count(UsageRecord.id).desc()
        ).limit(5).all()
        
        return [
            {"name": endpoint, "usage_count": count}
            for endpoint, count in endpoint_stats
        ]
    
    def get_user_analytics(self, db: Session, days: int = 30) -> UserAnalytics:
        """获取用户分析数据"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # 用户增长趋势
        user_growth = self._get_user_growth(db, start_date, end_date)
        
        # 用户活跃度
        user_activity = self._get_user_activity(db, start_date, end_date)
        
        # 用户留存率
        user_retention = self._calculate_user_retention(db)
        
        # 地理分布（模拟数据）
        geographic_distribution = [
            {"country": "中国", "users": 1200, "percentage": 60},
            {"country": "美国", "users": 400, "percentage": 20},
            {"country": "日本", "users": 200, "percentage": 10},
            {"country": "其他", "users": 200, "percentage": 10}
        ]
        
        return UserAnalytics(
            user_growth=user_growth,
            user_activity=user_activity,
            user_retention=user_retention,
            geographic_distribution=geographic_distribution
        )
    
    def _get_user_growth(self, db: Session, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取用户增长数据"""
        growth_data = []
        current_date = start_date
        
        while current_date <= end_date:
            users_count = db.query(User).filter(
                func.date(User.created_at) <= current_date.date()
            ).count()
            
            growth_data.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "total_users": users_count
            })
            
            current_date += timedelta(days=1)
        
        return growth_data
    
    def _get_user_activity(self, db: Session, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取用户活跃度数据"""
        activity_data = []
        current_date = start_date
        
        while current_date <= end_date:
            active_users = db.query(User).filter(
                func.date(User.last_login) == current_date.date()
            ).count()
            
            activity_data.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "active_users": active_users
            })
            
            current_date += timedelta(days=1)
        
        return activity_data
    
    def _calculate_user_retention(self, db: Session) -> Dict[str, float]:
        """计算用户留存率"""
        # 简化的留存率计算
        total_users = db.query(User).count()
        if total_users == 0:
            return {"day_1": 0, "day_7": 0, "day_30": 0}
        
        # 1天留存
        day_1_retained = db.query(User).filter(
            User.last_login >= datetime.utcnow() - timedelta(days=1)
        ).count()
        
        # 7天留存
        day_7_retained = db.query(User).filter(
            User.last_login >= datetime.utcnow() - timedelta(days=7)
        ).count()
        
        # 30天留存
        day_30_retained = db.query(User).filter(
            User.last_login >= datetime.utcnow() - timedelta(days=30)
        ).count()
        
        return {
            "day_1": (day_1_retained / total_users) * 100,
            "day_7": (day_7_retained / total_users) * 100,
            "day_30": (day_30_retained / total_users) * 100
        }
    
    def get_usage_analytics(self, db: Session, days: int = 30) -> UsageAnalytics:
        """获取使用分析数据"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # API调用趋势
        api_calls_trend = self._get_api_calls_trend(db, start_date, end_date)
        
        # 端点使用情况
        endpoint_usage = self._get_endpoint_usage(db, start_date, end_date)
        
        # 错误率（模拟数据）
        error_rates = self._generate_mock_error_rates(days)
        
        # 响应时间（模拟数据）
        response_times = self._generate_mock_response_times(days)
        
        return UsageAnalytics(
            api_calls_trend=api_calls_trend,
            endpoint_usage=endpoint_usage,
            error_rates=error_rates,
            response_times=response_times
        )
    
    def _get_api_calls_trend(self, db: Session, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取API调用趋势"""
        trend_data = []
        current_date = start_date
        
        while current_date <= end_date:
            calls_count = db.query(UsageRecord).filter(
                func.date(UsageRecord.timestamp) == current_date.date()
            ).count()
            
            trend_data.append({
                "date": current_date.strftime("%Y-%m-%d"),
                "api_calls": calls_count
            })
            
            current_date += timedelta(days=1)
        
        return trend_data
    
    def _get_endpoint_usage(self, db: Session, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取端点使用情况"""
        endpoint_stats = db.query(
            UsageRecord.endpoint,
            func.count(UsageRecord.id).label('count')
        ).filter(
            UsageRecord.timestamp >= start_date
        ).group_by(
            UsageRecord.endpoint
        ).order_by(
            func.count(UsageRecord.id).desc()
        ).all()
        
        return [
            {"endpoint": endpoint or "unknown", "count": count}
            for endpoint, count in endpoint_stats
        ]
    
    def _generate_mock_error_rates(self, days: int) -> List[Dict[str, Any]]:
        """生成模拟错误率数据"""
        import random
        error_data = []
        
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=days-i-1)).strftime("%Y-%m-%d")
            error_rate = random.uniform(0.5, 3.0)  # 0.5% - 3% 错误率
            
            error_data.append({
                "date": date,
                "error_rate": round(error_rate, 2)
            })
        
        return error_data
    
    def _generate_mock_response_times(self, days: int) -> List[Dict[str, Any]]:
        """生成模拟响应时间数据"""
        import random
        response_data = []
        
        for i in range(days):
            date = (datetime.utcnow() - timedelta(days=days-i-1)).strftime("%Y-%m-%d")
            avg_response_time = random.uniform(100, 500)  # 100-500ms
            
            response_data.append({
                "date": date,
                "avg_response_time": round(avg_response_time, 2)
            })
        
        return response_data
    
    def get_revenue_analytics(self, db: Session, months: int = 12) -> RevenueAnalytics:
        """获取收入分析数据"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=months*30)
        
        # 收入趋势
        revenue_trend = self._get_revenue_trend(db, start_date, end_date)
        
        # 套餐分布
        plan_distribution = self._get_plan_distribution(db)
        
        # 流失率（简化计算）
        churn_rate = self._calculate_churn_rate(db)
        
        # MRR和ARR
        mrr = self._calculate_mrr(db)
        arr = mrr * 12
        
        return RevenueAnalytics(
            revenue_trend=revenue_trend,
            plan_distribution=plan_distribution,
            churn_rate=churn_rate,
            mrr=mrr,
            arr=arr
        )
    
    def _get_revenue_trend(self, db: Session, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """获取收入趋势"""
        # 按月统计收入
        revenue_data = []
        current_date = start_date.replace(day=1)
        
        while current_date <= end_date:
            next_month = (current_date.replace(day=28) + timedelta(days=4)).replace(day=1)
            
            monthly_revenue = db.query(func.sum(Subscription.amount)).filter(
                and_(
                    Subscription.current_period_start >= current_date,
                    Subscription.current_period_start < next_month,
                    Subscription.status == "active"
                )
            ).scalar() or 0
            
            revenue_data.append({
                "month": current_date.strftime("%Y-%m"),
                "revenue": float(monthly_revenue)
            })
            
            current_date = next_month
        
        return revenue_data
    
    def _get_plan_distribution(self, db: Session) -> List[Dict[str, Any]]:
        """获取套餐分布"""
        plan_stats = db.query(
            Plan.name,
            func.count(Subscription.id).label('count')
        ).join(
            Subscription, Plan.id == Subscription.plan_id
        ).filter(
            Subscription.status == "active"
        ).group_by(
            Plan.name
        ).all()
        
        total_subscriptions = sum(count for _, count in plan_stats)
        
        return [
            {
                "plan": plan_name,
                "count": count,
                "percentage": (count / total_subscriptions * 100) if total_subscriptions > 0 else 0
            }
            for plan_name, count in plan_stats
        ]
    
    def _calculate_churn_rate(self, db: Session) -> float:
        """计算流失率"""
        # 简化的流失率计算：本月取消的订阅 / 上月活跃订阅
        this_month = datetime.utcnow().replace(day=1)
        last_month = (this_month - timedelta(days=1)).replace(day=1)
        
        cancelled_this_month = db.query(Subscription).filter(
            and_(
                Subscription.cancelled_at >= this_month,
                Subscription.cancelled_at < this_month + timedelta(days=32)
            )
        ).count()
        
        active_last_month = db.query(Subscription).filter(
            and_(
                Subscription.current_period_start <= last_month + timedelta(days=32),
                or_(
                    Subscription.cancelled_at.is_(None),
                    Subscription.cancelled_at >= this_month
                )
            )
        ).count()
        
        if active_last_month == 0:
            return 0
        
        return (cancelled_this_month / active_last_month) * 100
    
    def _calculate_mrr(self, db: Session) -> float:
        """计算月度经常性收入"""
        # 计算所有活跃的月度订阅收入
        monthly_subs = db.query(func.sum(Subscription.amount)).filter(
            and_(
                Subscription.status == "active",
                Subscription.billing_cycle == "monthly"
            )
        ).scalar() or 0
        
        # 年度订阅转换为月度
        yearly_subs = db.query(func.sum(Subscription.amount)).filter(
            and_(
                Subscription.status == "active",
                Subscription.billing_cycle == "yearly"
            )
        ).scalar() or 0
        
        return float(monthly_subs) + float(yearly_subs) / 12
    
    def get_organization_insights(self, db: Session, limit: int = 20) -> List[OrganizationInsights]:
        """获取组织洞察"""
        # 获取组织基本信息
        orgs = db.query(Organization).filter(Organization.is_active == True).limit(limit).all()
        
        insights = []
        for org in orgs:
            # 获取用户数量
            users_count = db.query(User).join(
                User.organizations
            ).filter(
                User.organizations.any(organization_id=org.id)
            ).count()
            
            # 获取本月API调用数
            month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            api_calls_month = db.query(UsageRecord).filter(
                and_(
                    UsageRecord.organization_id == org.id,
                    UsageRecord.timestamp >= month_start
                )
            ).count()
            
            # 获取存储使用量
            quota = db.query(UsageQuota).filter(UsageQuota.organization_id == org.id).first()
            storage_used = float(quota.storage_used_gb) if quota else 0
            
            # 获取最后活动时间
            last_activity = db.query(func.max(UsageRecord.timestamp)).filter(
                UsageRecord.organization_id == org.id
            ).scalar()
            
            # 计算健康评分（简化算法）
            health_score = self._calculate_health_score(users_count, api_calls_month, last_activity)
            
            insights.append(OrganizationInsights(
                id=org.id,
                name=org.name,
                plan=org.plan,
                users_count=users_count,
                api_calls_month=api_calls_month,
                storage_used_gb=storage_used,
                last_activity=last_activity,
                health_score=health_score
            ))
        
        return insights
    
    def _calculate_health_score(self, users_count: int, api_calls: int, last_activity: Optional[datetime]) -> float:
        """计算组织健康评分"""
        score = 0
        
        # 用户数量评分 (0-30分)
        if users_count > 0:
            score += min(users_count * 5, 30)
        
        # API使用评分 (0-40分)
        if api_calls > 0:
            score += min(api_calls / 100 * 10, 40)
        
        # 活跃度评分 (0-30分)
        if last_activity:
            days_since_activity = (datetime.utcnow() - last_activity).days
            if days_since_activity <= 1:
                score += 30
            elif days_since_activity <= 7:
                score += 20
            elif days_since_activity <= 30:
                score += 10
        
        return min(score, 100)

# 全局仪表板服务实例
dashboard_service = DashboardService()
