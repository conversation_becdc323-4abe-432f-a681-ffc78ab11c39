"""
Cognee服务模块 - 为项目提供AI记忆和知识图谱功能
"""
import os
import asyncio
import logging
from typing import List, Dict, Optional, Any
from pathlib import Path
import json
from datetime import datetime

# 设置日志
logger = logging.getLogger(__name__)

# 尝试导入cognee
try:
    import cognee
    COGNEE_AVAILABLE = True
    logger.info("Cognee库已成功导入")
except ImportError as e:
    logger.warning(f"Cognee库未安装或导入失败: {e}")
    COGNEE_AVAILABLE = False

class CogneeService:
    """Cognee服务类，提供AI记忆和知识管理功能"""
    
    def __init__(self, data_dir: str = "./cognee_data"):
        """
        初始化Cognee服务
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.initialized = False
        self.config = {}
        
    async def initialize(self, llm_api_key: str = None, llm_provider: str = "openai"):
        """
        初始化cognee环境
        
        Args:
            llm_api_key: LLM API密钥
            llm_provider: LLM提供商 (openai, anthropic等)
        """
        if not COGNEE_AVAILABLE:
            logger.error("Cognee不可用，无法初始化")
            return False
            
        try:
            # 设置环境变量
            if llm_api_key:
                if llm_provider == "openai":
                    os.environ["LLM_API_KEY"] = llm_api_key
                    os.environ["OPENAI_API_KEY"] = llm_api_key
                elif llm_provider == "anthropic":
                    os.environ["ANTHROPIC_API_KEY"] = llm_api_key
                    
            # 设置cognee配置
            self.config = {
                "llm_provider": llm_provider,
                "data_dir": str(self.data_dir),
                "initialized_at": datetime.now().isoformat()
            }
            
            # 测试cognee基本功能
            await self._test_cognee()
            
            self.initialized = True
            logger.info("Cognee服务初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"Cognee初始化失败: {e}")
            return False
    
    async def _test_cognee(self):
        """测试cognee基本功能"""
        try:
            # 添加一个测试文本
            test_text = "这是一个测试文本，用于验证cognee功能是否正常工作。"
            await cognee.add(test_text)
            
            # 生成知识图谱
            await cognee.cognify()
            
            # 测试搜索
            results = await cognee.search("测试")
            logger.info(f"Cognee测试成功，搜索结果: {len(results) if results else 0}条")
            
        except Exception as e:
            logger.warning(f"Cognee测试失败: {e}")
            raise
    
    async def add_text(self, text: str, metadata: Dict = None) -> bool:
        """
        添加文本到cognee知识库
        
        Args:
            text: 要添加的文本内容
            metadata: 可选的元数据
            
        Returns:
            bool: 是否成功添加
        """
        if not self.initialized or not COGNEE_AVAILABLE:
            logger.error("Cognee服务未初始化或不可用")
            return False
            
        try:
            # 如果有元数据，可以将其包含在文本中
            if metadata:
                enriched_text = f"{text}\n\n[元数据: {json.dumps(metadata, ensure_ascii=False)}]"
            else:
                enriched_text = text
                
            await cognee.add(enriched_text)
            logger.info(f"成功添加文本到cognee，长度: {len(text)}")
            return True
            
        except Exception as e:
            logger.error(f"添加文本到cognee失败: {e}")
            return False
    
    async def add_file(self, file_path: str) -> bool:
        """
        添加文件到cognee知识库
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功添加
        """
        if not self.initialized or not COGNEE_AVAILABLE:
            logger.error("Cognee服务未初始化或不可用")
            return False
            
        try:
            # 读取文件内容
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 添加文件元数据
            metadata = {
                "source_file": str(file_path),
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "added_at": datetime.now().isoformat()
            }
            
            return await self.add_text(content, metadata)
            
        except Exception as e:
            logger.error(f"添加文件到cognee失败: {e}")
            return False
    
    async def cognify(self) -> bool:
        """
        执行cognify操作，生成知识图谱
        
        Returns:
            bool: 是否成功执行
        """
        if not self.initialized or not COGNEE_AVAILABLE:
            logger.error("Cognee服务未初始化或不可用")
            return False
            
        try:
            await cognee.cognify()
            logger.info("Cognify操作执行成功")
            return True
            
        except Exception as e:
            logger.error(f"Cognify操作失败: {e}")
            return False
    
    async def search(self, query: str, limit: int = 5) -> List[Dict]:
        """
        在cognee知识库中搜索
        
        Args:
            query: 搜索查询
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if not self.initialized or not COGNEE_AVAILABLE:
            logger.error("Cognee服务未初始化或不可用")
            return []
            
        try:
            results = await cognee.search(query)
            
            # 格式化结果
            formatted_results = []
            if results:
                for i, result in enumerate(results[:limit]):
                    formatted_results.append({
                        "id": i,
                        "content": str(result),
                        "relevance_score": 1.0 - (i * 0.1),  # 简单的相关性评分
                        "source": "cognee",
                        "timestamp": datetime.now().isoformat()
                    })
            
            logger.info(f"搜索查询 '{query}' 返回 {len(formatted_results)} 条结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    async def add_conversation(self, user_message: str, assistant_response: str, 
                             session_id: str = "default") -> bool:
        """
        添加对话到cognee记忆中
        
        Args:
            user_message: 用户消息
            assistant_response: 助手回复
            session_id: 会话ID
            
        Returns:
            bool: 是否成功添加
        """
        if not self.initialized or not COGNEE_AVAILABLE:
            return False
            
        try:
            # 构建对话文本
            conversation_text = f"""
对话记录 (会话ID: {session_id})
时间: {datetime.now().isoformat()}

用户: {user_message}

助手: {assistant_response}
"""
            
            metadata = {
                "type": "conversation",
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "user_message": user_message,
                "assistant_response": assistant_response
            }
            
            return await self.add_text(conversation_text, metadata)
            
        except Exception as e:
            logger.error(f"添加对话到cognee失败: {e}")
            return False
    
    def get_status(self) -> Dict:
        """
        获取cognee服务状态
        
        Returns:
            Dict: 状态信息
        """
        return {
            "available": COGNEE_AVAILABLE,
            "initialized": self.initialized,
            "config": self.config,
            "data_dir": str(self.data_dir)
        }
    
    async def reset(self) -> bool:
        """
        重置cognee数据
        
        Returns:
            bool: 是否成功重置
        """
        if not COGNEE_AVAILABLE:
            return False
            
        try:
            # 这里可能需要根据cognee的API来实现重置功能
            # 目前先简单地重新初始化
            self.initialized = False
            logger.info("Cognee数据已重置")
            return True
            
        except Exception as e:
            logger.error(f"重置cognee数据失败: {e}")
            return False

# 全局cognee服务实例
cognee_service = CogneeService()
