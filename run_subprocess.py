import subprocess
import os
import sys

# 确保当前工作目录正确
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
os.chdir(ROOT_DIR)

# 设置环境变量使Python输出不被缓冲
env = os.environ.copy()
env["PYTHONUNBUFFERED"] = "1"

# 运行主应用程序并捕获输出
try:
    print("Starting MCP Market application...")
    process = subprocess.Popen(
        [sys.executable, "run.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        env=env
    )
    
    # 设置超时时间（秒）
    timeout = 10
    
    try:
        stdout, stderr = process.communicate(timeout=timeout)
        
        # 保存输出到文件
        with open("subprocess_stdout.log", "w") as f:
            f.write(stdout)
        
        with open("subprocess_stderr.log", "w") as f:
            f.write(stderr)
        
        print(f"Process exited with code: {process.returncode}")
        print(f"Output saved to subprocess_stdout.log and subprocess_stderr.log")
        
    except subprocess.TimeoutExpired:
        process.kill()
        print(f"Process did not complete within {timeout} seconds and was terminated.")
        stdout, stderr = process.communicate()
        
        # 保存部分输出到文件
        with open("subprocess_stdout_timeout.log", "w") as f:
            f.write(stdout)
        
        with open("subprocess_stderr_timeout.log", "w") as f:
            f.write(stderr)
        
        print(f"Partial output saved to subprocess_stdout_timeout.log and subprocess_stderr_timeout.log")
    
except Exception as e:
    print(f"Error running subprocess: {e}")
    
print("Subprocess script completed.") 