#!/usr/bin/env python3
"""
修复JavaScript错误的脚本
解决前端页面中的各种JavaScript问题
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_files():
    """检查必要的文件是否存在"""
    required_files = [
        "templates/index.html",
        "static/js/main.js",
        "static/img/favicon.svg"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"缺少文件: {missing_files}")
        return False
    
    logger.info("✅ 所有必要文件都存在")
    return True

def test_api_endpoints():
    """测试API端点是否正常"""
    try:
        import requests
        
        # 测试模型API
        try:
            response = requests.get('http://localhost:8000/api/models', timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 模型API正常，返回 {len(data)} 个模型")
            else:
                logger.warning(f"⚠️ 模型API返回状态码: {response.status_code}")
        except requests.exceptions.RequestException:
            logger.warning("⚠️ 无法连接到模型API（服务器可能未运行）")
        
        # 测试MCP服务器API
        try:
            response = requests.get('http://localhost:8000/api/mcp-servers', timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ MCP服务器API正常，返回 {len(data)} 个服务器")
            else:
                logger.warning(f"⚠️ MCP服务器API返回状态码: {response.status_code}")
        except requests.exceptions.RequestException:
            logger.warning("⚠️ 无法连接到MCP服务器API（服务器可能未运行）")
            
    except ImportError:
        logger.warning("⚠️ requests库未安装，跳过API测试")

def create_missing_directories():
    """创建缺少的目录"""
    directories = [
        "static/img",
        "static/css",
        "static/js"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 确保目录存在: {directory}")

def validate_javascript():
    """验证JavaScript语法"""
    js_files = [
        "static/js/main.js",
        "static/js/chat.js"
    ]
    
    for js_file in js_files:
        if Path(js_file).exists():
            logger.info(f"✅ JavaScript文件存在: {js_file}")
        else:
            logger.warning(f"⚠️ JavaScript文件不存在: {js_file}")

def check_html_template():
    """检查HTML模板"""
    template_file = "templates/index.html"
    
    if not Path(template_file).exists():
        logger.error(f"❌ HTML模板不存在: {template_file}")
        return False
    
    # 检查是否包含必要的函数
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'showMainChat',
        'switchToPage', 
        'showAgentConfig',
        'showHelp',
        'updateSidebarSelection'
    ]
    
    missing_functions = []
    for func in required_functions:
        if f'function {func}' not in content:
            missing_functions.append(func)
    
    if missing_functions:
        logger.warning(f"⚠️ HTML模板中缺少函数: {missing_functions}")
    else:
        logger.info("✅ HTML模板包含所有必要的函数")
    
    return True

def show_fixes_applied():
    """显示已应用的修复"""
    print("\n🔧 已应用的修复:")
    print("✅ 添加了缺失的JavaScript函数:")
    print("   - showMainChat() - 显示主聊天界面")
    print("   - switchToPage() - 页面切换")
    print("   - showAgentConfig() - 显示智能体配置")
    print("   - showHelp() - 显示帮助文档")
    print("   - updateSidebarSelection() - 更新侧边栏选中状态")
    print()
    print("✅ 修复了模型加载问题:")
    print("   - 处理API返回对象而非数组的情况")
    print("   - 添加了数据类型检查和转换")
    print()
    print("✅ 添加了favicon:")
    print("   - 创建了SVG格式的favicon")
    print("   - 更新了HTML模板中的favicon引用")
    print()
    print("✅ 增强了错误处理:")
    print("   - 添加了空值检查")
    print("   - 改进了DOM元素访问")

def show_usage_tips():
    """显示使用提示"""
    print("\n💡 使用提示:")
    print("1. 启动应用: python app.py")
    print("2. 访问主页: http://localhost:8000")
    print("3. 如果仍有JavaScript错误，请检查浏览器控制台")
    print("4. 确保所有API端点正常工作")
    print()
    print("🔍 调试步骤:")
    print("1. 打开浏览器开发者工具 (F12)")
    print("2. 查看Console标签页的错误信息")
    print("3. 查看Network标签页的API请求状态")
    print("4. 如有问题，检查服务器日志")

def main():
    """主修复流程"""
    print("🔧 JavaScript错误修复工具")
    print("=" * 50)
    
    # 1. 检查文件
    if not check_files():
        logger.error("❌ 文件检查失败")
        return
    
    # 2. 创建缺少的目录
    create_missing_directories()
    
    # 3. 验证JavaScript
    validate_javascript()
    
    # 4. 检查HTML模板
    check_html_template()
    
    # 5. 测试API端点
    test_api_endpoints()
    
    # 6. 显示修复信息
    show_fixes_applied()
    
    # 7. 显示使用提示
    show_usage_tips()
    
    print("\n🎉 JavaScript错误修复完成!")
    print("现在可以启动应用并测试功能了。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        logger.error(f"修复过程中出错: {e}")
