#!/usr/bin/env python3
"""
安装ChromaDB
"""

import subprocess
import sys

def install_chromadb():
    """安装ChromaDB"""
    print("安装ChromaDB...")
    
    try:
        # 安装ChromaDB
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "chromadb>=0.4.0", 
            "--no-cache-dir"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("ChromaDB安装成功")
            print(result.stdout)
        else:
            print("ChromaDB安装失败")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("安装超时")
        return False
    except Exception as e:
        print(f"安装异常: {e}")
        return False

def test_chromadb():
    """测试ChromaDB"""
    try:
        import chromadb
        print("ChromaDB导入成功")
        
        # 创建客户端
        client = chromadb.Client()
        print("ChromaDB客户端创建成功")
        
        return True
    except Exception as e:
        print(f"ChromaDB测试失败: {e}")
        return False

if __name__ == "__main__":
    if install_chromadb():
        if test_chromadb():
            print("ChromaDB安装和测试完成")
        else:
            print("ChromaDB测试失败")
    else:
        print("ChromaDB安装失败")
