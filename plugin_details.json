{"success": true, "message": null, "data": {"id": "e6b770b4-3995-4372-8cb0-5a34b254fc70", "name": "c2517771-a9e0-42f0-8a87-30fb5ce5049b", "version": "0.1.0", "description": "# AKShare One MCP Server", "status": "installed", "source": "git", "source_url": "https://gitee.com/luoda_123/akshare-mcp.git", "local_path": "C:\\Users\\<USER>\\PycharmProjects2\\run-mcp\\mcp_market_data\\plugins\\c2517771-a9e0-42f0-8a87-30fb5ce5049b", "dependency_group": "group_e6b770b4-3995-4372-8cb0-5a34b254fc70", "entry_point": "main.py", "api_url": null, "error_message": null, "metadata": {"inferred": true}}}