# Cognee集成故障排除指南

## 常见问题及解决方案

### 1. API密钥相关问题

#### 问题：`LLM API key is not set`
```
InvalidValueError: LLM API key is not set. (Status code: 422)
Connection to LLM could not be established.
```

**解决方案：**

1. **检查API密钥格式**
   ```bash
   # OpenAI API密钥格式示例
   sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   
   # 确保没有多余的空格或换行符
   ```

2. **设置环境变量**
   ```bash
   # Windows (PowerShell)
   $env:LLM_API_KEY="your-openai-api-key"
   $env:OPENAI_API_KEY="your-openai-api-key"
   
   # Windows (CMD)
   set LLM_API_KEY=your-openai-api-key
   set OPENAI_API_KEY=your-openai-api-key
   
   # Linux/Mac
   export LLM_API_KEY="your-openai-api-key"
   export OPENAI_API_KEY="your-openai-api-key"
   ```

3. **在Web界面中配置**
   - 访问 `http://localhost:8000/cognee`
   - 输入API密钥
   - 勾选"跳过功能测试"选项
   - 点击初始化

4. **验证API密钥有效性**
   ```bash
   # 测试OpenAI API密钥
   curl -H "Authorization: Bearer your-api-key" \
        https://api.openai.com/v1/models
   ```

### 2. 初始化失败问题

#### 问题：初始化过程中出现各种错误

**解决方案：**

1. **使用简化初始化**
   - 在Web界面中勾选"跳过功能测试"
   - 这将跳过初始的功能测试，只设置基本配置

2. **检查网络连接**
   ```bash
   # 测试网络连接
   ping api.openai.com
   ```

3. **检查防火墙设置**
   - 确保允许访问 `api.openai.com`
   - 检查代理设置

### 3. 依赖安装问题

#### 问题：导入cognee失败

**解决方案：**

1. **重新安装cognee**
   ```bash
   pip uninstall cognee
   pip install cognee>=0.1.42
   ```

2. **安装所有依赖**
   ```bash
   python install_cognee.py
   ```

3. **手动安装依赖**
   ```bash
   pip install neo4j>=5.0.0
   pip install chromadb>=0.4.0
   pip install sentence-transformers>=2.2.0
   pip install networkx>=3.0
   ```

### 4. 内存和性能问题

#### 问题：内存不足或运行缓慢

**解决方案：**

1. **增加系统内存**
   - 推荐至少8GB RAM

2. **优化批处理大小**
   - 减少一次性添加的文档数量
   - 分批处理大文件

3. **清理旧数据**
   ```bash
   # 删除cognee数据目录
   rm -rf ./cognee_data
   mkdir ./cognee_data
   ```

### 5. 搜索结果为空

#### 问题：搜索不返回任何结果

**解决方案：**

1. **确保已执行cognify**
   - 添加内容后必须点击"生成知识图谱"
   - 等待处理完成

2. **检查搜索词**
   - 使用更通用的搜索词
   - 尝试不同的关键词

3. **重新生成知识图谱**
   - 删除旧数据
   - 重新添加内容
   - 重新执行cognify

### 6. 文件上传问题

#### 问题：文件上传失败

**解决方案：**

1. **检查文件格式**
   - 支持：.txt, .md, .pdf, .docx
   - 确保文件编码为UTF-8

2. **检查文件大小**
   - 避免上传过大的文件
   - 建议单个文件小于10MB

3. **检查文件权限**
   - 确保文件可读
   - 检查临时目录权限

### 7. 服务状态检查

#### 检查Cognee服务状态

```python
# 运行状态检查脚本
python test_cognee_integration.py
```

#### 检查API端点

```bash
# 测试状态端点
curl http://localhost:8000/api/cognee/status

# 测试搜索端点（需要先初始化）
curl -X POST http://localhost:8000/api/cognee/search \
     -H "Content-Type: application/json" \
     -d '{"query": "测试", "limit": 3}'
```

### 8. 日志和调试

#### 查看详细日志

1. **应用日志**
   ```bash
   # 启动应用时查看日志
   python app.py
   ```

2. **Cognee日志**
   ```bash
   # 检查cognee数据目录中的日志
   ls -la ./cognee_data/logs/
   ```

#### 启用调试模式

```python
# 在cognee_service.py中设置日志级别
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 9. 版本兼容性问题

#### 检查版本

```bash
# 检查Python版本
python --version

# 检查cognee版本
pip show cognee

# 检查其他依赖版本
pip list | grep -E "(neo4j|chromadb|sentence-transformers|networkx)"
```

#### 更新到兼容版本

```bash
# 更新cognee
pip install --upgrade cognee

# 更新所有依赖
pip install --upgrade -r requirements.txt
```

### 10. 重置和清理

#### 完全重置Cognee

1. **通过Web界面重置**
   - 访问 `http://localhost:8000/cognee`
   - 点击"重置记忆数据"

2. **手动清理**
   ```bash
   # 删除数据目录
   rm -rf ./cognee_data
   
   # 重新创建目录
   mkdir ./cognee_data
   ```

3. **重新安装**
   ```bash
   # 卸载并重新安装
   pip uninstall cognee
   python install_cognee.py
   ```

## 获取帮助

### 社区支持

- **Cognee GitHub**: https://github.com/topoteretes/cognee
- **Cognee Discord**: https://discord.gg/NQPKmU5CCg
- **文档**: https://docs.cognee.ai

### 报告问题

如果问题仍然存在，请提供以下信息：

1. **系统信息**
   - 操作系统版本
   - Python版本
   - 已安装的包版本

2. **错误信息**
   - 完整的错误堆栈
   - 相关日志

3. **重现步骤**
   - 详细的操作步骤
   - 使用的数据示例

### 临时解决方案

如果Cognee功能暂时无法使用，你仍然可以：

1. **使用基本聊天功能**
   - 所有聊天功能正常工作
   - MCP服务器功能不受影响

2. **使用传统知识库**
   - 访问 `http://localhost:8000/knowledge-base`
   - 使用LangChain的RAG功能

3. **手动管理记忆**
   - 将重要对话保存到文件
   - 稍后重新导入到Cognee
