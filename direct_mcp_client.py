"""
Direct MCP client implementation without dependencies on mcp-agent
"""
import asyncio
import json
import logging
import subprocess
import os
import platform
import sys
import socket
from typing import Dict, List, Any, Optional, Union

logger = logging.getLogger(__name__)

# Import HTTP libraries with fallbacks
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    logger.warning("aiohttp not available - falling back to requests")
    AIOHTTP_AVAILABLE = False

# Global flag for HTTP client choice
USE_AIOHTTP = AIOHTTP_AVAILABLE
if platform.system() == 'Windows' and AIOHTTP_AVAILABLE:
    try:
        # Try to import aiodns - if it fails, we'll use requests instead
        import aiodns
        USE_AIOHTTP = True
    except ImportError:
        logger.warning("aiodns not available on Windows - HTTP requests will use synchronous requests library")
        USE_AIOHTTP = False
    except Exception as e:
        logger.warning(f"Error initializing aiodns: {e} - falling back to synchronous requests")
        USE_AIOHTTP = False

# Try to import requests as a fallback
REQUESTS_AVAILABLE = False
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    logger.warning("requests not available - HTTP server communication may fail")

# If neither aiohttp nor requests is available, we'll use a stubbed version
if not AIOHTTP_AVAILABLE and not REQUESTS_AVAILABLE:
    logger.warning("Neither aiohttp nor requests available - creating stub HTTP client")
    
    class StubResponse:
        def __init__(self):
            self.status = 500
            self.status_code = 500
            
        async def json(self):
            return {"error": "HTTP client libraries not available"}
            
        async def text(self):
            return "HTTP client libraries not available"
            
        def json(self):
            return {"error": "HTTP client libraries not available"}
            
        def text(self):
            return "HTTP client libraries not available"
    
    # Stub version of requests.get and requests.post
    def stub_request(*args, **kwargs):
        return StubResponse()
        
    # Make the stubs available globally
    if not REQUESTS_AVAILABLE:
        class StubRequests:
            @staticmethod
            def get(*args, **kwargs):
                logger.error("Attempted HTTP GET without requests library")
                return stub_request(*args, **kwargs)
                
            @staticmethod
            def post(*args, **kwargs):
                logger.error("Attempted HTTP POST without requests library")
                return stub_request(*args, **kwargs)
        
        requests = StubRequests()

class DirectMCPClient:
    """Direct implementation of MCP client without depending on mcp-agent package"""
    
    def __init__(self, server_name: str, server_config: Dict[str, Any]):
        """Initialize the MCP client with server configuration"""
        self.server_name = server_name
        self.config = server_config
        self.connection_type = server_config.get("connection_type", "stdio")
        self.process = None
        self.tools_cache = None
    
    async def get_tools(self) -> List[Dict[str, Any]]:
        """Fetch available tools from the MCP server"""
        # Use cached tools if available
        if self.tools_cache is not None:
            return self.tools_cache
            
        if self.connection_type == "stdio":
            return await self._get_tools_stdio()
        elif self.connection_type in ["sse", "streamableHttp"]:
            return await self._get_tools_http()
        else:
            logger.error(f"Unsupported connection type: {self.connection_type}")
            return []
    
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        if self.connection_type == "stdio":
            return await self._call_tool_stdio(tool_name, params)
        elif self.connection_type in ["sse", "streamableHttp"]:
            return await self._call_tool_http(tool_name, params)
        else:
            logger.error(f"Unsupported connection type: {self.connection_type}")
            return {"error": f"不支持的连接类型: {self.connection_type}"}
    
    async def _get_tools_stdio(self) -> List[Dict[str, Any]]:
        """Get tools from a stdio-based MCP server"""
        try:
            # Start the server process if not running
            if not self.process:
                await self._start_process()
            
            # Send a tools request
            request = {"type": "tools_request"}
            await self._write_to_process(json.dumps(request))
            
            # Read the response
            response_text = await self._read_from_process()
            response = json.loads(response_text)
            
            # Cache the tools
            if "tools" in response:
                self.tools_cache = response["tools"]
                return self.tools_cache
            else:
                logger.error(f"Invalid response from server: {response}")
                return []
        except Exception as e:
            logger.error(f"Error getting tools via stdio: {e}")
            return []
    
    async def _call_tool_stdio(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a stdio-based MCP server"""
        try:
            # Start the server process if not running
            if not self.process:
                await self._start_process()
            
            # Send a tool call request
            request = {
                "type": "tool_call",
                "tool": tool_name,
                "params": params
            }
            await self._write_to_process(json.dumps(request))
            
            # Read the response
            response_text = await self._read_from_process()
            return json.loads(response_text)
        except Exception as e:
            logger.error(f"Error calling tool via stdio: {e}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def _get_tools_http(self) -> List[Dict[str, Any]]:
        """Get tools from an HTTP-based MCP server"""
        try:
            url = self.config.get("url", "")
            if not url:
                logger.error("No URL configured for HTTP server")
                return []
            
            # Ensure URL ends with /tools
            if not url.endswith("/tools"):
                base_url = url.rstrip("/")
                tools_url = f"{base_url}/tools"
            else:
                tools_url = url
            
            headers = self.config.get("headers", {})
            timeout = self.config.get("timeout", 30)
            
            # If neither HTTP client is available, return empty tool list
            if not AIOHTTP_AVAILABLE and not REQUESTS_AVAILABLE:
                logger.error("Cannot get tools: No HTTP client library available")
                return []
            
            # Decide which HTTP client to use
            if USE_AIOHTTP:
                try:
                    # Try using aiohttp
                    async with aiohttp.ClientSession() as session:
                        async with session.get(tools_url, headers=headers, timeout=timeout) as response:
                            if response.status == 200:
                                tools_data = await response.json()
                                self.tools_cache = tools_data
                                return tools_data
                            else:
                                error_text = await response.text()
                                logger.error(f"Error getting tools via HTTP: {response.status} - {error_text}")
                                return []
                except Exception as aiohttp_err:
                    # If aiohttp fails, fallback to synchronous requests
                    logger.warning(f"aiohttp request failed, falling back to requests: {aiohttp_err}")
                    if REQUESTS_AVAILABLE:
                        return await self._get_tools_sync(tools_url, headers, timeout)
                    else:
                        logger.error("Cannot fallback to requests: library not available")
                        return []
            else:
                # Use synchronous requests directly
                if REQUESTS_AVAILABLE:
                    return await self._get_tools_sync(tools_url, headers, timeout)
                else:
                    logger.error("Cannot use requests: library not available")
                    return []
                
        except Exception as e:
            logger.error(f"Error getting tools via HTTP: {e}")
            return []
    
    async def _get_tools_sync(self, url: str, headers: Dict[str, str], timeout: int) -> List[Dict[str, Any]]:
        """Fallback method to get tools using synchronous requests library"""
        try:
            # Run the request in an executor to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.get(url, headers=headers, timeout=timeout)
            )
            
            if response.status_code == 200:
                tools_data = response.json()
                self.tools_cache = tools_data
                return tools_data
            else:
                logger.error(f"Error getting tools via sync HTTP: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            logger.error(f"Sync request failed: {e}")
            return []
    
    async def _call_tool_http(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on an HTTP-based MCP server"""
        try:
            url = self.config.get("url", "")
            if not url:
                logger.error("No URL configured for HTTP server")
                return {"error": "未配置URL"}
            
            # Ensure URL ends with /message
            if not url.endswith("/message"):
                base_url = url.rstrip("/")
                message_url = f"{base_url}/message"
            else:
                message_url = url
            
            headers = self.config.get("headers", {})
            if "Content-Type" not in headers:
                headers["Content-Type"] = "application/json"
                
            timeout = self.config.get("timeout", 30)
            
            # Prepare request data
            request_data = {
                "tool": tool_name,
                "params": params
            }
            
            # If neither HTTP client is available, return error
            if not AIOHTTP_AVAILABLE and not REQUESTS_AVAILABLE:
                logger.error("Cannot call tool: No HTTP client library available")
                return {"error": "无法调用HTTP工具 - 未安装HTTP客户端库"}
            
            # Decide which HTTP client to use
            if USE_AIOHTTP:
                try:
                    # Try using aiohttp
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            message_url, 
                            json=request_data, 
                            headers=headers, 
                            timeout=timeout
                        ) as response:
                            if response.status == 200:
                                return await response.json()
                            else:
                                error_text = await response.text()
                                logger.error(f"Error calling tool via HTTP: {response.status} - {error_text}")
                                return {
                                    "error": f"HTTP错误 {response.status}",
                                    "details": error_text
                                }
                except Exception as aiohttp_err:
                    # If aiohttp fails, fallback to synchronous requests
                    logger.warning(f"aiohttp request failed, falling back to requests: {aiohttp_err}")
                    if REQUESTS_AVAILABLE:
                        return await self._call_tool_sync(message_url, request_data, headers, timeout)
                    else:
                        logger.error("Cannot fallback to requests: library not available")
                        return {"error": "aiohttp失败，并且未安装requests库作为备选"}
            else:
                # Use synchronous requests directly
                if REQUESTS_AVAILABLE:
                    return await self._call_tool_sync(message_url, request_data, headers, timeout)
                else:
                    logger.error("Cannot use requests: library not available")
                    return {"error": "未安装requests库"}
                
        except Exception as e:
            logger.error(f"Error calling tool via HTTP: {e}")
            return {"error": f"工具调用失败: {str(e)}"}
            
    async def _call_tool_sync(self, url: str, data: Dict[str, Any], headers: Dict[str, str], timeout: int) -> Dict[str, Any]:
        """Fallback method to call a tool using synchronous requests library"""
        try:
            # Run the request in an executor to avoid blocking
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(url, json=data, headers=headers, timeout=timeout)
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"HTTP错误 {response.status_code}",
                    "details": response.text
                }
        except Exception as e:
            logger.error(f"Sync request failed: {e}")
            return {"error": f"同步请求失败: {str(e)}"}
    
    async def _start_process(self):
        """Start the stdio server process"""
        try:
            command = self.config.get("command", "")
            args = self.config.get("args", [])
            env = self.config.get("env", {})
            cwd = self.config.get("cwd")
            
            if not command:
                raise ValueError("No command specified for stdio server")
            
            # Prepare environment
            full_env = os.environ.copy()
            full_env.update(env)
            
            # Start the process
            cmd_list = [command] + args
            logger.info(f"Starting MCP server: {' '.join(cmd_list)}")
            
            self.process = await asyncio.create_subprocess_exec(
                *cmd_list,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=full_env,
                cwd=cwd
            )
            
            # Read initial output if any
            await self._read_initial_output()
            
        except Exception as e:
            logger.error(f"Error starting MCP server process: {e}")
            raise
    
    async def _read_initial_output(self):
        """Read initial output from the server"""
        if not self.process or not self.process.stdout:
            return
            
        # Set a timeout for initial output
        try:
            # Wait briefly for any initial output
            await asyncio.wait_for(
                self._read_until_ready(),
                timeout=2.0
            )
        except asyncio.TimeoutError:
            # It's okay if there's no initial output
            pass
    
    async def _read_until_ready(self):
        """Read output until the server indicates it's ready"""
        if not self.process or not self.process.stdout:
            return
            
        while True:
            line = await self.process.stdout.readline()
            if not line:
                break
                
            line_str = line.decode('utf-8', errors='replace').strip()
            logger.debug(f"Server output: {line_str}")
            
            # Check if the server is ready (this is a common pattern in MCP servers)
            if "ready" in line_str.lower() or "started" in line_str.lower():
                logger.info("MCP server is ready")
                break
    
    async def _write_to_process(self, data: str):
        """Write data to the process stdin"""
        if not self.process or not self.process.stdin:
            raise RuntimeError("Process not started or stdin not available")
            
        # Ensure the data ends with a newline
        if not data.endswith('\n'):
            data += '\n'
            
        self.process.stdin.write(data.encode('utf-8'))
        await self.process.stdin.drain()
    
    async def _read_from_process(self) -> str:
        """Read data from the process stdout"""
        if not self.process or not self.process.stdout:
            raise RuntimeError("Process not started or stdout not available")
            
        # Read until we get a complete JSON object
        buffer = ""
        brackets_count = 0
        in_string = False
        escape_next = False
        
        async def read_char():
            char_bytes = await self.process.stdout.read(1)
            if not char_bytes:
                raise EOFError("End of file reached")
            return char_bytes.decode('utf-8', errors='replace')
        
        try:
            while True:
                char = await read_char()
                buffer += char
                
                if escape_next:
                    escape_next = False
                elif char == '\\':
                    escape_next = True
                elif char == '"' and not escape_next:
                    in_string = not in_string
                elif not in_string:
                    if char == '{':
                        brackets_count += 1
                    elif char == '}':
                        brackets_count -= 1
                        if brackets_count == 0 and buffer.strip().startswith('{'):
                            # We have a complete JSON object
                            return buffer
                
                # Safety check to prevent buffer overflow
                if len(buffer) > 1024 * 1024:  # 1MB limit
                    raise ValueError("Response too large")
        except Exception as e:
            logger.error(f"Error reading from process: {e}")
            return "{}"
    
    async def close(self):
        """Close the client and cleanup resources"""
        if self.process:
            try:
                # Try to terminate gracefully
                self.process.terminate()
                try:
                    await asyncio.wait_for(self.process.wait(), timeout=2.0)
                except asyncio.TimeoutError:
                    # Force kill if necessary
                    self.process.kill()
            except Exception as e:
                logger.error(f"Error closing process: {e}")
            self.process = None

# Client factory function
def create_direct_client(server_name: str, server_config: Dict[str, Any]) -> DirectMCPClient:
    """Create a direct MCP client for the given server configuration"""
    return DirectMCPClient(server_name, server_config) 