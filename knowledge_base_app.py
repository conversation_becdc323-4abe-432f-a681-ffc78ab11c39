import streamlit as st
import os
from langchain.document_loaders import PyPDFLoader, DirectoryLoader, UnstructuredFileLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from langchain.vectorstores import Chroma
from langchain.chains import RetrievalQA
from langchain.llms import HuggingFacePipeline # Or any other LLM integration you prefer
import tempfile

# --- Configuration ---
DOCUMENTS_DIR = "kb_documents"
VECTORSTORE_DIR = "vectorstore_db"
EMBEDDING_MODEL_NAME = "sentence-transformers/all-MiniLM-L6-v2"
# Placeholder for LLM - you'll need to configure this
# For example, using a local model with HuggingFacePipeline
# Or using OpenAI, Cohere, etc. (would require API keys and different Langchain LLM objects)
LLM_MODEL_ID = "google/flan-t5-base" # Example, replace with your choice

# --- Helper Functions ---

def load_documents(docs_path):
    """Loads documents from the specified directory."""
    # Using DirectoryLoader with UnstructuredFileLoader for broader file type support
    # You might want to add specific loaders for .txt, .md, etc. if needed
    # and handle them accordingly.
    # For now, focusing on PDF and general unstructured files.

    all_docs = []
    if not os.path.exists(docs_path):
        os.makedirs(docs_path)
        st.info(f"Created documents directory: {docs_path}")
        return []

    # Generic loader for various file types
    # Using glob to pick up various file types, customize as needed
    generic_loader = DirectoryLoader(docs_path, glob="**/*.*", loader_cls=UnstructuredFileLoader, show_progress=True, use_multithreading=True, silent_errors=True)
    try:
        loaded_docs = generic_loader.load()
        if loaded_docs:
            all_docs.extend(loaded_docs)
            st.write(f"Loaded {len(loaded_docs)} files using UnstructuredFileLoader.")
    except Exception as e:
        st.error(f"Error loading files with UnstructuredFileLoader: {e}")
        # Fallback or specific loaders can be added here
        # For example, explicitly trying PyPDFLoader for .pdf files if Unstructured fails or is not preferred

    # Clean up None results if silent_errors=True caused them
    all_docs = [doc for doc in all_docs if doc is not None]
    return all_docs


def split_text(documents):
    """Splits documents into manageable chunks."""
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=150)
    texts = text_splitter.split_documents(documents)
    return texts

def get_embeddings_model():
    """Initializes and returns the embedding model."""
    embeddings = HuggingFaceEmbeddings(model_name=EMBEDDING_MODEL_NAME)
    return embeddings

def get_vector_store(texts, embeddings, persist_directory=VECTORSTORE_DIR):
    """Creates or loads the vector store."""
    if not os.path.exists(persist_directory) or not os.listdir(persist_directory):
        st.info(f"Creating new vector store in {persist_directory}...")
        db = Chroma.from_documents(texts, embeddings, persist_directory=persist_directory)
        db.persist()
        st.success("Vector store created and persisted.")
    else:
        st.info(f"Loading existing vector store from {persist_directory}...")
        db = Chroma(persist_directory=persist_directory, embedding_function=embeddings)
        st.success("Vector store loaded.")
    return db

def get_qa_chain(db, llm_model):
    """Creates a Question-Answering chain."""
    retriever = db.as_retriever(search_kwargs={"k": 3}) # Retrieve top 3 relevant chunks
    # Note: Configuring the LLM (llm_model) is crucial here.
    # This is a placeholder and needs actual LLM setup.
    # For a local HuggingFace model:
    # from transformers import pipeline
    # hf_pipeline = pipeline("text2text-generation", model=LLM_MODEL_ID) # device=0 for GPU
    # llm = HuggingFacePipeline(pipeline=hf_pipeline)
    # For OpenAI:
    # from langchain.chat_models import ChatOpenAI
    # llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0) # Requires OPENAI_API_KEY

    # ---- VERY IMPORTANT: LLM Setup ----
    # The following is a NON-FUNCTIONAL placeholder for the LLM
    # You MUST replace this with a proper Langchain LLM instance.
    # For demonstration, we'll use a placeholder that just returns a fixed string.
    # THIS IS NOT A REAL LLM.
    class PlaceholderLLM:
        def __init__(self):
            pass
        def __call__(self, prompt, stop=None):
            return "This is a placeholder response. Please configure a real LLM."

        def _llm_type(self): # Required by some Langchain components
            return "placeholder"

    # llm_placeholder = PlaceholderLLM() # Replace this!
    # For now, we will try to initialize a HuggingFacePipeline as an example
    try:
        from transformers import pipeline
        hf_pipeline = pipeline("text2text-generation", model=LLM_MODEL_ID)
        llm = HuggingFacePipeline(pipeline=hf_pipeline)
        st.success(f"Successfully loaded LLM: {LLM_MODEL_ID}")
    except Exception as e:
        st.error(f"Failed to load LLM '{LLM_MODEL_ID}'. Using a placeholder. Error: {e}")
        st.warning("Query answering will be non-functional. Please ensure the LLM model is accessible and libraries are installed.")
        class PlaceholderLLM: # Define it again for scope
            def __init__(self): pass
            def __call__(self, prompt, stop=None): return "Placeholder LLM: Configure a real LLM for answers."
            def _llm_type(self): return "placeholder"
        llm = PlaceholderLLM()


    qa_chain = RetrievalQA.from_chain_type(
        llm=llm,
        chain_type="stuff", # Options: "stuff", "map_reduce", "refine", "map_rerank"
        retriever=retriever,
        return_source_documents=True
    )
    return qa_chain

# --- Streamlit UI ---
st.set_page_config(page_title="Knowledge Base LLM", layout="wide")
st.title("📚 Knowledge Base Query with LLM")

# Ensure documents directory exists
if not os.path.exists(DOCUMENTS_DIR):
    os.makedirs(DOCUMENTS_DIR)

# File Uploader
st.sidebar.header("Upload Documents")
uploaded_files = st.sidebar.file_uploader("Upload PDF or other documents", type=['pdf', 'txt', 'md', 'docx'], accept_multiple_files=True)

if uploaded_files:
    temp_dir = tempfile.mkdtemp(dir=DOCUMENTS_DIR) # Create a temporary subdirectory for this upload batch
    for uploaded_file in uploaded_files:
        try:
            # Save uploaded file to the temporary directory
            with open(os.path.join(temp_dir, uploaded_file.name), "wb") as f:
                f.write(uploaded_file.getbuffer())
            st.sidebar.success(f"Successfully saved: {uploaded_file.name}")
        except Exception as e:
            st.sidebar.error(f"Error saving {uploaded_file.name}: {e}")

    st.sidebar.info(f"Files saved to temporary processing directory: {temp_dir}")
    st.sidebar.warning("Note: For persistence across sessions, files should be in the main DOCUMENTS_DIR or the vector store needs to be pre-built.")


# Processing and Vector Store Initialization Section
st.header("Setup & Processing")

# Use a session state to store the QA chain and vector store to avoid reloading on every interaction
if 'qa_chain' not in st.session_state:
    st.session_state.qa_chain = None
if 'vector_db' not in st.session_state:
    st.session_state.vector_db = None
if 'embeddings_model' not in st.session_state:
    st.session_state.embeddings_model = get_embeddings_model() # Load embeddings model once


if st.button("Process Documents and Initialize/Load Knowledge Base"):
    with st.spinner("Loading documents..."):
        docs = load_documents(DOCUMENTS_DIR) # Load from the base KB directory

    if not docs:
        st.warning("No documents found in the knowledge base directory or failed to load. Please upload documents or add them to the 'kb_documents' folder.")
    else:
        with st.spinner("Splitting documents into chunks..."):
            texts = split_text(docs)
            st.write(f"Number of text chunks: {len(texts)}")

        with st.spinner("Initializing vector store... (This may take a while for new documents)"):
            st.session_state.vector_db = get_vector_store(texts, st.session_state.embeddings_model)

        if st.session_state.vector_db:
            with st.spinner("Initializing QA chain..."):
                # The LLM will be loaded inside get_qa_chain
                st.session_state.qa_chain = get_qa_chain(st.session_state.vector_db, LLM_MODEL_ID)
                st.success("Knowledge Base and QA Chain Ready!")
        else:
            st.error("Failed to initialize vector store.")


# Query Section
st.header("Query the Knowledge Base")
query = st.text_input("Enter your question:")

if query:
    if st.session_state.qa_chain:
        with st.spinner("Searching for answer..."):
            try:
                result = st.session_state.qa_chain({"query": query})
                st.subheader("Answer:")
                st.write(result["result"])

                st.subheader("Sources:")
                for source_doc in result["source_documents"]:
                    st.markdown(f"**Source:** `{source_doc.metadata.get('source', 'N/A')}` (Page: `{source_doc.metadata.get('page', 'N/A')}`)")
                    # st.text_area("Content Snippet:", value=source_doc.page_content, height=100, disabled=True) # Might be too verbose
                    st.markdown("---")
            except Exception as e:
                st.error(f"Error during query processing: {e}")
                st.error("Make sure you have processed documents and the LLM is correctly configured and accessible.")
    else:
        st.warning("Please process documents and initialize the knowledge base first.")

st.sidebar.markdown("---")
st.sidebar.markdown("### Instructions:")
st.sidebar.markdown("1. Upload documents (PDF, TXT, MD, DOCX) using the sidebar.")
st.sidebar.markdown("2. (Important) After uploading, or if you have documents in `kb_documents` folder, click the **'Process Documents and Initialize/Load Knowledge Base'** button.")
st.sidebar.markdown("3. Once initialized, ask questions in the text box above.")
st.sidebar.markdown(f"Documents are loaded from: `{os.path.abspath(DOCUMENTS_DIR)}`")
st.sidebar.markdown(f"Vector store is persisted in: `{os.path.abspath(VECTORSTORE_DIR)}`")

# Note: For a production app, consider:
# - More robust error handling.
# - Asynchronous processing for large documents/batches.
# - More sophisticated LLM loading and management (e.g. API keys via .env).
# - Option to choose different embedding/LLM models.
# - Clearing the temporary upload directory or a strategy for adding temp files to the main KB.
# - Better UI/UX for managing documents in the KB. 