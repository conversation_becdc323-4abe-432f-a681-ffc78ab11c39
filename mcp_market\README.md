# MCP插件市场

轻量级、资源共享的MCP服务插件生态系统。

## 功能特点

- **插件管理**: 从Git仓库安装、更新和卸载MCP服务插件
- **资源共享**: 多个MCP服务共享Python环境，节省资源
- **依赖管理**: 智能管理Python依赖，避免冲突
- **集中监控**: 统一界面监控和管理所有MCP服务
- **简易部署**: 一键安装和启动MCP服务

## 系统要求

- Python 3.8或更高版本
- Git客户端
- Windows/Linux/macOS

## 快速开始

### Windows

1. 下载或克隆本仓库
2. 运行`start_marketplace.bat`
3. 打开浏览器访问`http://localhost:8000`

### Linux/macOS

1. 下载或克隆本仓库
2. 执行以下命令:

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate   # Linux/macOS
# venv\Scripts\activate.bat  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动服务
python run.py
```

3. 打开浏览器访问`http://localhost:8000`

## 使用指南

### 添加插件

1. 点击侧边栏的"插件市场"
2. 点击"添加插件"按钮
3. 输入Git仓库URL（支持GitHub、GitLab等）
4. 可选择分支（默认为主分支）
5. 点击"添加"按钮

### 安装插件

1. 在插件市场中找到要安装的插件
2. 点击"安装"按钮
3. 系统会自动创建或选择合适的依赖组
4. 等待安装完成

### 启动插件

1. 点击侧边栏的"已安装插件"
2. 找到要启动的插件
3. 点击"启动"按钮

### 查看运行中的插件

1. 点击侧边栏的"运行中插件"
2. 可以查看、重启或停止服务
3. 点击日志图标可查看服务日志

### 管理依赖组

1. 点击侧边栏的"依赖管理"
2. 查看现有依赖组或创建新依赖组
3. 点击"查看"按钮可查看详细信息

## 配置选项

启动时可以通过命令行参数配置:

```
python run.py --host 0.0.0.0 --port 8000 --data-dir ./mcp_market_data --plugin-port-base 9000
```

参数说明:
- `--host`: 监听主机地址（默认：0.0.0.0）
- `--port`: 监听端口（默认：8000）
- `--data-dir`: 数据目录（默认：./mcp_market_data）
- `--plugin-port-base`: 插件服务端口基数（默认：9000）

## 目录结构

```
mcp_market/
├── api/              # API接口模块
├── core/             # 核心功能模块
├── models/           # 数据模型
├── services/         # 服务管理模块
├── static/           # 静态资源
├── templates/        # 页面模板
└── app.py            # 应用入口
```

## 插件开发

要开发兼容MCP插件市场的插件，请遵循以下规范:

1. 创建标准的Python项目结构
2. 在根目录添加`mcp_manifest.json`文件，包含以下信息:

```json
{
  "name": "插件名称",
  "version": "1.0.0",
  "description": "插件描述",
  "entry_point": "main.py",
  "author": "作者名称",
  "license": "许可证",
  "requires_python": ">=3.8",
  "dependencies": [
    "flask>=2.0.0",
    "requests>=2.25.0"
  ],
  "mcp_protocol_version": "1.0"
}
```

3. 在入口点文件中使用环境变量获取端口:

```python
import os
port = int(os.environ.get("MCP_PORT", 5000))
```

## 许可证

MIT License 