#!/usr/bin/env python3
"""
探测8000端口服务器的脚本
"""

import requests
import json

def probe_endpoint(url, method='GET', headers=None, data=None):
    """探测单个端点"""
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=5)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data, timeout=5)
        else:
            return None
            
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'content': response.text[:200] if response.text else '',
            'success': True
        }
    except requests.exceptions.RequestException as e:
        return {
            'error': str(e),
            'success': False
        }

def main():
    """主函数"""
    print("🔍 探测 http://127.0.0.1:8000 服务器")
    print("=" * 50)
    
    # 常见的MCP端点
    endpoints = [
        '/',
        '/health',
        '/sse',
        '/mcp',
        '/mcp/sse',
        '/api',
        '/status',
        '/akshare',
        '/akshare/sse',
        '/v1',
        '/v1/sse'
    ]
    
    # 常见的请求头
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'MCP-Health-Check/1.0'
    }
    
    results = {}
    
    for endpoint in endpoints:
        url = f"http://127.0.0.1:8000{endpoint}"
        print(f"\n📍 测试: {url}")
        
        # GET请求
        result = probe_endpoint(url, 'GET', headers)
        if result['success']:
            print(f"   ✅ GET {result['status_code']}")
            if result['content']:
                print(f"   📝 内容: {result['content'][:100]}...")
            if result['status_code'] == 200:
                results[endpoint] = result
        else:
            print(f"   ❌ GET 失败: {result['error']}")
            
        # 对于可能的SSE端点，尝试特殊头
        if 'sse' in endpoint:
            sse_headers = headers.copy()
            sse_headers['Accept'] = 'text/event-stream'
            sse_headers['Cache-Control'] = 'no-cache'
            
            result = probe_endpoint(url, 'GET', sse_headers)
            if result['success']:
                print(f"   🔄 SSE {result['status_code']}")
                if result['status_code'] == 200:
                    results[f"{endpoint}_sse"] = result
    
    print("\n" + "=" * 50)
    print("📊 探测结果汇总:")
    
    if results:
        print("✅ 成功的端点:")
        for endpoint, result in results.items():
            print(f"   • {endpoint}: HTTP {result['status_code']}")
    else:
        print("❌ 没有找到可用的端点")
        print("\n💡 可能的原因:")
        print("   1. akshare MCP服务器配置错误")
        print("   2. 端点路径不是标准的")
        print("   3. 需要特殊的认证或请求头")
        print("   4. 服务器只支持特定的MCP协议版本")
        
    print("\n🔧 建议:")
    print("   1. 检查akshare-one-mcp的文档")
    print("   2. 查看服务器启动日志")
    print("   3. 尝试其他连接类型（stdio）")
    print("   4. 联系akshare-one-mcp开发者")

if __name__ == "__main__":
    main() 