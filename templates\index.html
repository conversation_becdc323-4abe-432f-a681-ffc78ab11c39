<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NexusAI Hub - 企业级AI对话平台</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.ico">
    <!-- 添加Google字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50 h-screen flex font-sans">
    <!-- 产品引导遮罩层 -->
    <div id="onboardingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center h-full">
            <div class="bg-white rounded-2xl p-8 max-w-md mx-4 shadow-2xl">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                        <i class="fas fa-brain text-white text-3xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎使用 NexusAI Hub</h2>
                    <p class="text-gray-600">企业级AI对话平台，让AI成为您的智能助手</p>
                </div>
                <div class="space-y-4 mb-6">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-check text-green-600 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">多模型支持</h3>
                            <p class="text-sm text-gray-600">支持OpenAI、Anthropic等主流AI模型</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-plug text-purple-600 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">MCP工具集成</h3>
                            <p class="text-sm text-gray-600">通过MCP协议扩展AI能力边界</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fas fa-shield-alt text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800">企业级安全</h3>
                            <p class="text-sm text-gray-600">本地部署，数据安全有保障</p>
                        </div>
                    </div>
                </div>
                <button onclick="closeOnboarding()" class="w-full bg-gradient-to-r from-indigo-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                    开始使用
                </button>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="w-80 bg-white shadow-xl flex flex-col border-r border-gray-100">
        <!-- 品牌头部 -->
        <div class="p-6 bg-gradient-to-br from-indigo-600 via-indigo-700 to-purple-700 text-white relative overflow-hidden">
            <div class="absolute top-0 right-0 w-40 h-40 bg-white opacity-5 rounded-full -mr-20 -mt-20"></div>
            <div class="absolute bottom-0 left-0 w-32 h-32 bg-white opacity-5 rounded-full -ml-16 -mb-16"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-indigo-500 opacity-10 rounded-full"></div>
            <div class="relative">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg rounded-xl flex items-center justify-center mr-3">
                        <i class="fas fa-brain text-white text-xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold tracking-tight">NexusAI Hub</h1>
                </div>
                <p class="text-indigo-100 text-sm">企业级AI对话平台 v2.1</p>
                <div class="flex items-center mt-3 space-x-3">
                    <span class="text-xs bg-white bg-opacity-20 backdrop-filter backdrop-blur-lg px-2 py-1 rounded-full">
                        <i class="fas fa-circle text-green-300 text-xs mr-1"></i>系统正常
                    </span>
                    <button onclick="showSystemInfo()" class="text-xs text-indigo-200 hover:text-white transition-colors">
                        <i class="fas fa-info-circle"></i> 系统信息
                    </button>
                </div>
            </div>
        </div>

        <!-- 快速操作栏 -->
        <div class="px-4 py-3 bg-indigo-50 border-b border-indigo-100">
            <div class="flex items-center justify-between">
                <span class="text-xs font-medium text-indigo-500 uppercase tracking-wider">导航菜单</span>
                <div class="flex space-x-1">
                    <button onclick="showSettings()" class="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all" title="设置">
                        <i class="fas fa-cog text-sm"></i>
                    </button>
                    <button onclick="toggleTheme()" id="themeToggle" class="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all" title="切换主题">
                        <i class="fas fa-moon text-sm"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <div class="p-3">
            <div class="space-y-1">
                <button onclick="switchToPage('chatContainer', 'chat')" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-indigo-50 hover:text-indigo-600 transition-colors" data-item="chat">
                    <i class="fas fa-comment-alt mr-3 text-indigo-500"></i>
                    <span class="font-medium">对话</span>
                    </button>
                <button onclick="switchToPage('modelConfigArea', 'model'); loadModels()" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-emerald-50 hover:text-emerald-600 transition-colors" data-item="model">
                    <i class="fas fa-brain mr-3 text-emerald-500"></i>
                    <span class="font-medium">模型配置</span>
                    </button>
                <button onclick="switchToPage('mcpConfigArea', 'mcp'); loadMCPServers()" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-purple-50 hover:text-purple-600 transition-colors" data-item="mcp">
                    <i class="fas fa-plug mr-3 text-purple-500"></i>
                    <span class="font-medium">MCP服务器</span>
                </button>
                <button onclick="window.location.href='/integrated'" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-purple-50 hover:text-purple-600 transition-colors" data-item="mcp-market">
                    <i class="fas fa-store mr-3 text-purple-500"></i>
                    <span class="font-medium">MCP插件市场</span>
                </button>
                <button onclick="window.location.href='/cognee'" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-indigo-50 hover:text-indigo-600 transition-colors" data-item="cognee">
                    <i class="fas fa-brain mr-3 text-indigo-500"></i>
                    <span class="font-medium">AI记忆系统</span>
                </button>
                <button onclick="showAgentConfig()" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-blue-50 hover:text-blue-600 transition-colors" data-item="agent">
                    <i class="fas fa-robot mr-3 text-blue-500"></i>
                    <span class="font-medium">智能体</span>
                </button>
                <button onclick="showHelp()" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-blue-50 hover:text-blue-600 transition-colors" data-item="help">
                    <i class="fas fa-question-circle mr-3 text-blue-500"></i>
                    <span class="font-medium">帮助文档</span>
                    </button>
                </div>
            </div>

        <!-- 底部信息 -->
        <div class="mt-auto p-4 border-t border-gray-100 bg-gray-50">
            <div class="flex flex-col space-y-2">
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-md flex items-center justify-center">
                        <i class="fas fa-brain text-white text-xs"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">NexusAI Hub</span>
                    <span class="text-xs text-gray-500">v2.1</span>
                </div>
                <div class="flex space-x-3 text-xs text-gray-500">
                    <a href="#" onclick="showDocs()" class="hover:text-indigo-600 transition-colors">文档</a>
                    <a href="#" onclick="showPrivacy()" class="hover:text-indigo-600 transition-colors">隐私政策</a>
                    <a href="https://github.com/nexusai/hub" target="_blank" class="hover:text-indigo-600 transition-colors">
                        <i class="fab fa-github mr-1"></i>GitHub
                    </a>
                </div>
                <div class="text-xs text-gray-500">© 2024 NexusAI Hub</div>
            </div>
        </div>
        </div>

    <!-- 主聊天区域 -->
    <div class="flex-1 flex flex-col bg-gray-50">
        <!-- 顶部导航栏 -->
        <div class="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <!-- 模型选择器 -->
                    <div class="flex items-center space-x-3">
                        <label class="text-sm font-medium text-gray-600">AI模型</label>
                        <div class="relative">
                            <select id="selectedModel" class="pl-4 pr-10 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none min-w-[200px] font-medium">
                                <option value="">请选择模型</option>
                            </select>
                            <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>
                    </div>
                    
                    <!-- 智能体选择器 -->
                    <div class="flex items-center space-x-3 pl-6 border-l border-gray-200">
                        <label class="text-sm font-medium text-gray-600">智能体</label>
                        <div class="relative">
                            <select id="selectedAgent" class="pl-4 pr-10 py-2.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none min-w-[180px] font-medium">
                                <option value="">选择智能体</option>
                                <!-- 动态加载 -->
                            </select>
                            <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                        </div>
                    </div>

                    <!-- 功能开关 -->
                    <div class="flex items-center space-x-4 pl-6 border-l border-gray-200">
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="streamToggle" class="sr-only peer" checked>
                            <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                            <span class="ml-2 text-sm font-medium text-gray-700">流式输出</span>
                        </label>
                        
                        <label class="inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="contextToggle" class="sr-only peer" checked>
                            <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                            <span class="ml-2 text-sm font-medium text-gray-700">上下文记忆</span>
                        </label>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-3">
                    <button onclick="exportChat()" class="text-gray-500 hover:text-gray-700 p-2 hover:bg-gray-100 rounded-lg transition-all" title="导出对话">
                        <i class="fas fa-download"></i>
                        </button>
                    <button onclick="shareChat()" class="text-gray-500 hover:text-gray-700 p-2 hover:bg-gray-100 rounded-lg transition-all" title="分享对话">
                        <i class="fas fa-share-alt"></i>
                    </button>
                    <div class="w-px h-6 bg-gray-200"></div>
                    <button onclick="newConversation()" class="bg-blue-50 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-100 transition-all flex items-center font-medium">
                        <i class="fas fa-plus mr-2"></i>新建对话
                        </button>
                </div>
            </div>
        </div>

        <!-- 智能体思考面板 -->
        <div id="agentThinkingPanel" class="hidden bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
            <div class="p-4 max-w-4xl mx-auto">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-2">
                            <i class="fas fa-brain text-white text-sm"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-semibold text-gray-800" id="agentThinkingName">智能体思考过程</h3>
                            <p class="text-xs text-gray-500" id="agentThinkingStep">步骤 0/0</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="collapseThinkingBtn" onclick="toggleThinkingPanel()" class="text-xs bg-white text-gray-600 px-2 py-1 rounded border border-gray-200 hover:bg-gray-50">
                            <i class="fas fa-chevron-up mr-1"></i>隐藏
                        </button>
                        <button onclick="clearAgentThinking()" class="text-xs bg-white text-gray-600 px-2 py-1 rounded border border-gray-200 hover:bg-gray-50">
                            <i class="fas fa-eraser mr-1"></i>清除
                        </button>
                    </div>
                </div>
                <div class="bg-white rounded-lg border border-blue-100 p-3 max-h-64 overflow-y-auto" id="agentThinkingContent">
                    <div class="text-center text-gray-500 py-4">
                        <i class="fas fa-robot text-blue-300 text-3xl mb-2"></i>
                        <p>智能体尚未开始思考</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- MCP工具栏 -->
        <div id="mcpToolbar" class="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-3 border-b border-gray-200 transition-all">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <span class="text-sm font-medium text-gray-700">
                        <i class="fas fa-toolbox mr-2 text-purple-500"></i>MCP工具
                    </span>
                    <div id="mcpServerCheckboxes" class="flex flex-wrap gap-2">
                        <!-- 动态加载MCP服务器选择 -->
                    </div>
                </div>
                <div class="flex items-center gap-3">
                    <!-- 全局健康检查指示器 -->
                    <div id="globalHealthCheckIndicator" class="hidden">
                        <div class="loading-spinner"></div>
                    </div>
                    <button onclick="showMCPTools()" class="text-sm bg-white text-purple-700 px-3 py-1.5 rounded-lg hover:bg-purple-50 transition-all border border-purple-200 font-medium">
                        <i class="fas fa-info-circle mr-1"></i>查看工具详情
                    </button>
                </div>
            </div>
        </div>

        <!-- 内容区域 - 使用Flex和绝对定位来实现页面切换 -->
        <div class="flex-1 overflow-hidden relative">
            <!-- 主聊天区域 -->
            <div id="chatContainer" class="absolute inset-0 overflow-y-auto">
                <div class="max-w-4xl mx-auto py-6 px-4">
                    <!-- 欢迎消息 -->
                    <div id="welcomeMessage" class="text-center py-12">
                        <div class="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-3xl mx-auto mb-6 flex items-center justify-center">
                            <i class="fas fa-comments text-4xl text-blue-600"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-3">开始您的AI对话之旅</h2>
                        <p class="text-gray-600 mb-8 max-w-md mx-auto">选择一个AI模型，输入您的问题，让AI助手为您提供专业的解答和帮助。</p>
                        
                        <!-- 快速提示 -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-2xl mx-auto">
                            <button onclick="quickPrompt('写一篇关于人工智能的文章')" class="p-3 bg-white hover:bg-blue-50 rounded-xl border border-gray-200 hover:border-blue-300 transition-all group">
                                <i class="fas fa-pen text-blue-500 mb-2 group-hover:scale-110 transition-transform"></i>
                                <div class="text-sm font-medium text-gray-700">写作助手</div>
                            </button>
                            <button onclick="quickPrompt('解释量子计算的原理')" class="p-3 bg-white hover:bg-green-50 rounded-xl border border-gray-200 hover:border-green-300 transition-all group">
                                <i class="fas fa-graduation-cap text-green-500 mb-2 group-hover:scale-110 transition-transform"></i>
                                <div class="text-sm font-medium text-gray-700">知识问答</div>
                            </button>
                            <button onclick="quickPrompt('帮我分析这段代码')" class="p-3 bg-white hover:bg-purple-50 rounded-xl border border-gray-200 hover:border-purple-300 transition-all group">
                                <i class="fas fa-code text-purple-500 mb-2 group-hover:scale-110 transition-transform"></i>
                                <div class="text-sm font-medium text-gray-700">代码分析</div>
                            </button>
                            <button onclick="quickPrompt('制定一个学习计划')" class="p-3 bg-white hover:bg-orange-50 rounded-xl border border-gray-200 hover:border-orange-300 transition-all group">
                                <i class="fas fa-tasks text-orange-500 mb-2 group-hover:scale-110 transition-transform"></i>
                                <div class="text-sm font-medium text-gray-700">任务规划</div>
                            </button>
                        </div>
                    </div>

                    <!-- 聊天消息列表 -->
                    <div id="messageList" class="space-y-4">
                        <!-- 动态加载消息 -->
                    </div>
                </div>
            </div>
            
            <!-- 设置页面 -->
            <div id="settingsArea" class="absolute inset-0 overflow-y-auto bg-gray-50 hidden">
                <div class="max-w-4xl mx-auto py-6 px-4">
                    <div class="mb-6 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-gray-700 to-gray-900 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                <i class="fas fa-cog text-white text-xl"></i>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-800">系统设置</h2>
                                <p class="text-gray-500">自定义您的 NexusAI Hub 使用体验</p>
                            </div>
                        </div>
                        <button onclick="closeSettings()" class="p-2 rounded-full hover:bg-gray-200">
                            <i class="fas fa-times text-gray-500"></i>
                        </button>
                    </div>
                    
                    <!-- 设置内容区域 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 左侧导航 -->
                        <div class="bg-white rounded-xl shadow-sm p-4 h-fit">
                            <h3 class="text-sm font-medium text-gray-400 uppercase tracking-wider mb-3">设置选项</h3>
                            <div class="space-y-1">
                                <button class="w-full text-left px-3 py-2 rounded bg-blue-50 text-blue-700 font-medium" onclick="showSettingsTab('general')">
                                    <i class="fas fa-sliders-h mr-2"></i>常规设置
                                </button>
                                <button class="w-full text-left px-3 py-2 rounded text-gray-700 hover:bg-gray-50" onclick="showSettingsTab('appearance')">
                                    <i class="fas fa-paint-brush mr-2"></i>外观与主题
                                </button>
                                <button class="w-full text-left px-3 py-2 rounded text-gray-700 hover:bg-gray-50" onclick="showSettingsTab('privacy')">
                                    <i class="fas fa-shield-alt mr-2"></i>隐私与安全
                                </button>
                                <button class="w-full text-left px-3 py-2 rounded text-gray-700 hover:bg-gray-50" onclick="showSettingsTab('advanced')">
                                    <i class="fas fa-code mr-2"></i>高级选项
                                </button>
                                <button class="w-full text-left px-3 py-2 rounded text-gray-700 hover:bg-gray-50" onclick="showSettingsTab('about')">
                                    <i class="fas fa-info-circle mr-2"></i>关于系统
                                </button>
                            </div>
                        </div>
                        
                        <!-- 右侧设置内容 -->
                        <div class="col-span-2 bg-white rounded-xl shadow-sm p-6">
                            <!-- 常规设置 -->
                            <div id="generalSettings" class="settings-tab">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">常规设置</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <label class="flex items-center justify-between mb-2">
                                            <span class="font-medium text-gray-700">默认语言</span>
                                        </label>
                                        <select name="language" class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="zh">简体中文</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="flex items-center justify-between mb-2">
                                            <span class="font-medium text-gray-700">开启声音通知</span>
                                            <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                                <input type="checkbox" name="soundNotification" id="soundNotification" class="sr-only peer">
                                                <div class="w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                            </div>
                                        </label>
                                        <p class="text-xs text-gray-500">收到新消息时播放通知声音</p>
                                    </div>
                                    
                                    <div>
                                        <label class="flex items-center justify-between mb-2">
                                            <span class="font-medium text-gray-700">自动保存会话</span>
                                            <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                                <input type="checkbox" name="autoSave" id="autoSave" class="sr-only peer" checked>
                                                <div class="w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                            </div>
                                        </label>
                                        <p class="text-xs text-gray-500">自动保存会话历史以便下次访问</p>
                                    </div>
                                    
                                    <div>
                                        <label class="font-medium text-gray-700 mb-2 block">自动清理历史记录</label>
                                        <select name="autoClear" class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="never">从不清理</option>
                                            <option value="7days">7天前的记录</option>
                                            <option value="30days">30天前的记录</option>
                                            <option value="90days">90天前的记录</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">系统将自动清理指定时间前的历史记录</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 外观与主题设置 -->
                            <div id="appearanceSettings" class="settings-tab hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">外观与主题</h3>
                                
                                <div class="space-y-6">
                                    <div>
                                        <label class="font-medium text-gray-700 mb-2 block">主题模式</label>
                                        <div class="grid grid-cols-3 gap-3">
                                            <label class="theme-option bg-white p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-all">
                                                <input type="radio" name="theme" value="light" class="sr-only">
                                                <div class="p-2 bg-white border border-gray-200 rounded mb-2"></div>
                                                <span class="block text-center text-sm font-medium text-gray-700">浅色模式</span>
                                            </label>
                                            <label class="theme-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-all">
                                                <input type="radio" name="theme" value="dark" class="sr-only">
                                                <div class="p-2 bg-gray-800 border border-gray-700 rounded mb-2"></div>
                                                <span class="block text-center text-sm font-medium text-gray-700">深色模式</span>
                                            </label>
                                            <label class="theme-option p-3 border border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-all">
                                                <input type="radio" name="theme" value="auto" class="sr-only" checked>
                                                <div class="p-2 bg-gradient-to-r from-white to-gray-800 border border-gray-200 rounded mb-2"></div>
                                                <span class="block text-center text-sm font-medium text-gray-700">跟随系统</span>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="font-medium text-gray-700 mb-2 block">主色调</label>
                                        <div class="grid grid-cols-6 gap-2">
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #4f46e5;">
                                                <input type="radio" name="accentColor" value="indigo" class="sr-only" checked>
                                            </label>
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #2563eb;">
                                                <input type="radio" name="accentColor" value="blue" class="sr-only">
                                            </label>
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #0ea5e9;">
                                                <input type="radio" name="accentColor" value="sky" class="sr-only">
                                            </label>
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #10b981;">
                                                <input type="radio" name="accentColor" value="emerald" class="sr-only">
                                            </label>
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #8b5cf6;">
                                                <input type="radio" name="accentColor" value="violet" class="sr-only">
                                            </label>
                                            <label class="color-option w-full aspect-square rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-offset-2 hover:ring-blue-500 transition-all" style="background-color: #ec4899;">
                                                <input type="radio" name="accentColor" value="pink" class="sr-only">
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <label class="font-medium text-gray-700 mb-2 block">字体大小</label>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs text-gray-500">小</span>
                                            <input type="range" min="12" max="20" value="16" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                            <span class="text-xs text-gray-500">大</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 隐私设置 -->
                            <div id="privacySettings" class="settings-tab hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">隐私与安全</h3>
                                
                                <!-- 隐私设置内容 -->
                            </div>
                            
                            <!-- 高级设置 -->
                            <div id="advancedSettings" class="settings-tab hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">高级选项</h3>
                                
                                <!-- 高级设置内容 -->
                            </div>
                            
                            <!-- 关于系统 -->
                            <div id="aboutSettings" class="settings-tab hidden">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">关于系统</h3>
                                
                                <!-- 关于系统内容 -->
                            </div>
                            
                            <!-- 设置页底部操作按钮 -->
                            <div class="mt-8 pt-6 border-t border-gray-100 flex justify-end space-x-3">
                                <button onclick="resetSettings()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all">
                                    恢复默认设置
                                </button>
                                <button onclick="saveSettings()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all">
                                    保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 模型配置页面 -->
            <div id="modelConfigArea" class="absolute inset-0 overflow-y-auto bg-gray-50 hidden">
                <div class="max-w-4xl mx-auto py-6 px-4">
                    <div class="mb-6 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                    <i class="fas fa-brain text-white"></i>
                                </div>
                                <div>
                                <h2 class="text-xl font-bold text-gray-800">AI模型配置</h2>
                                <p class="text-sm text-gray-500">管理您的AI模型接入</p>
                                </div>
                            </div>
                        <button onclick="showMainChat()" class="p-2 rounded-full hover:bg-gray-200">
                            <i class="fas fa-times text-gray-500"></i>
                            </button>
                        </div>
                        
                        <!-- 模型快速添加 -->
                        <div class="mb-4">
                            <button onclick="toggleModelForm()" class="w-full bg-white hover:bg-gray-50 text-gray-700 py-2.5 px-4 rounded-xl border border-gray-200 transition-all flex items-center justify-center group">
                                <i class="fas fa-plus-circle mr-2 text-emerald-500 group-hover:scale-110 transition-transform"></i>
                                <span class="font-medium">添加新模型</span>
                            </button>
                        </div>

                        <!-- 模型添加表单（默认隐藏） -->
                    <div id="modelForm" class="hidden space-y-3 mb-4 p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">模型名称</label>
                                    <input type="text" id="modelName" placeholder="如：GPT-4" 
                                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                                </div>
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">提供商</label>
                                    <select id="modelProvider" class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm" onchange="updateProviderDefaults()">
                                        <option value="">选择提供商</option>
                                        <option value="openai">OpenAI</option>
                                        <option value="anthropic">Anthropic</option>
                                        <option value="deepseek">DeepSeek</option>
                                        <option value="qwen">通义千问</option>
                                        <option value="zhipu">智谱AI</option>
                                        <option value="moonshot">月之暗面</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label class="text-xs font-medium text-gray-600 mb-1 block">API密钥</label>
                                <div class="relative">
                                    <input type="password" id="modelApiKey" placeholder="输入您的API密钥" 
                                           class="w-full px-3 py-2 pr-10 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                                    <button type="button" onclick="togglePasswordVisibility('modelApiKey')" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                                <input type="text" id="modelApiUrl" placeholder="API地址 (可选)" 
                                       class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                                <input type="text" id="modelModelName" placeholder="模型标识 (可选)" 
                                       class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                            </div>
                            
                            <div class="flex space-x-2">
                                <button onclick="addModel()" class="flex-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white py-2 px-4 rounded-lg hover:shadow-md transition-all text-sm font-medium">
                                    <i class="fas fa-check mr-1"></i>确认添加
                                </button>
                                <button onclick="toggleModelForm()" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all text-sm font-medium">
                                    取消
                                </button>
                            </div>
                        </div>

                        <!-- 已配置的模型列表 -->
                    <div id="modelList" class="space-y-2 bg-white rounded-lg p-4 shadow-sm">
                            <!-- 动态加载 -->
                        <div class="text-center p-4 text-gray-500">加载模型列表...</div>
                    </div>
                </div>
            </div>

            <!-- MCP配置页面 -->
            <div id="mcpConfigArea" class="absolute inset-0 overflow-y-auto bg-gray-50 hidden">
                <div class="max-w-4xl mx-auto py-6 px-4">
                    <div class="mb-6 flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                    <i class="fas fa-plug text-white"></i>
                                </div>
                                <div>
                                <h2 class="text-xl font-bold text-gray-800">MCP服务器配置</h2>
                                <p class="text-sm text-gray-500">管理您的MCP服务器</p>
                                </div>
                            </div>
                        <div class="flex items-center">
                            <!-- 添加健康检查指示器 -->
                            <div id="healthCheckIndicator" class="hidden mr-3">
                                <div class="loading-spinner"></div>
                            </div>
                            <button onclick="showMainChat()" class="p-2 rounded-full hover:bg-gray-200">
                                <i class="fas fa-times text-gray-500"></i>
                            </button>
                        </div>
                        </div>

                        <!-- MCP服务器快速添加 -->
                        <div class="mb-4">
                            <button onclick="toggleMCPForm()" class="w-full bg-white hover:bg-gray-50 text-gray-700 py-2.5 px-4 rounded-xl border border-gray-200 transition-all flex items-center justify-center group">
                                <i class="fas fa-plus-circle mr-2 text-purple-500 group-hover:scale-110 transition-transform"></i>
                                <span class="font-medium">添加新服务器</span>
                            </button>
                        </div>

                        <!-- MCP服务器添加表单（默认隐藏） -->
                    <div id="mcpForm" class="hidden space-y-3 mb-4 p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <!-- 快速模板选择 -->
                            <div class="mb-3">
                                <label class="text-xs font-medium text-gray-600 mb-2 block">快速选择模板</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button onclick="loadMCPExample('fetch')" class="p-2 bg-gray-50 hover:bg-purple-50 hover:border-purple-200 rounded-lg border border-gray-200 transition-all text-xs">
                                        <i class="fas fa-globe text-purple-500 mb-1"></i>
                                        <div class="font-medium">Fetch</div>
                                    </button>
                                    <button onclick="loadMCPExample('filesystem')" class="p-2 bg-gray-50 hover:bg-purple-50 hover:border-purple-200 rounded-lg border border-gray-200 transition-all text-xs">
                                        <i class="fas fa-folder text-purple-500 mb-1"></i>
                                        <div class="font-medium">文件系统</div>
                                    </button>
                                    <button onclick="loadMCPExample('sqlite')" class="p-2 bg-gray-50 hover:bg-purple-50 hover:border-purple-200 rounded-lg border border-gray-200 transition-all text-xs">
                                        <i class="fas fa-database text-purple-500 mb-1"></i>
                                        <div class="font-medium">SQLite</div>
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">服务名称</label>
                                    <input type="text" id="mcpName" placeholder="服务标识" 
                                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                </div>
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">连接类型</label>
                                    <select id="mcpConnectionType" onchange="toggleMCPConnectionFields()" 
                                            class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                        <option value="stdio">标准IO</option>
                                        <option value="sse">SSE</option>
                                        <option value="streamableHttp">HTTP流</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="text-xs font-medium text-gray-600 mb-1 block">描述</label>
                                <input type="text" id="mcpDescription" placeholder="服务功能描述" 
                                       class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                            </div>

                            <!-- stdio 配置 -->
                            <div id="stdioConfig" class="space-y-2">
                                <div class="grid grid-cols-2 gap-2">
                                    <input type="text" id="mcpCommand" placeholder="命令 (如: uvx)" 
                                           class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                    <input type="text" id="mcpArgs" placeholder="参数 (逗号分隔)" 
                                           class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                </div>
                                
                                <!-- 环境变量配置 -->
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">环境变量</label>
                                    <div id="envVarsContainer" class="space-y-2">
                                        <!-- 动态添加的环境变量行 -->
                                    </div>
                                    <button type="button" onclick="addEnvVar()" class="mt-2 text-sm text-purple-600 hover:text-purple-800">
                                        <i class="fas fa-plus-circle"></i> 添加环境变量
                                    </button>
                                </div>
                            </div>

                            <!-- HTTP 配置 -->
                            <div id="httpConfig" class="hidden space-y-2">
                                <input type="url" id="mcpUrl" placeholder="服务URL" 
                                       class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                                
                                <!-- 请求头配置 -->
                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">请求头</label>
                                    <div id="headersContainer" class="space-y-2">
                                        <!-- 动态添加的请求头行 -->
                                    </div>
                                    <button type="button" onclick="addHeaderRow()" class="mt-2 text-sm text-purple-600 hover:text-purple-800">
                                        <i class="fas fa-plus-circle"></i> 添加请求头
                                    </button>
                                </div>
                            </div>

                            <div class="flex space-x-2">
                                <button onclick="addMCPServer()" class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-2 px-4 rounded-lg hover:shadow-md transition-all text-sm font-medium">
                                    <i class="fas fa-check mr-1"></i>确认添加
                                </button>
                                <button onclick="toggleMCPForm()" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all text-sm font-medium">
                                    取消
                                </button>
                            </div>
                        </div>

                        <!-- 已配置的MCP服务器列表 -->
                    <div id="mcpList" class="space-y-2 bg-white rounded-lg p-4 shadow-sm">
                            <!-- 动态加载 -->
                        <div class="text-center p-4 text-gray-500">加载MCP服务器列表...</div>
                </div>
            </div>
        </div>

            <!-- 智能体配置页面 -->
            <div id="agentConfigArea" class="absolute inset-0 overflow-y-auto bg-gray-50 hidden">
                <div class="max-w-4xl mx-auto py-6 px-4">
                    <div class="mb-6 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                                <i class="fas fa-robot text-white"></i>
                </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-800">AI智能体配置</h2>
                                <p class="text-sm text-gray-500">创建和管理您的AI智能体</p>
            </div>
        </div>
                        <button onclick="showMainChat()" class="p-2 rounded-full hover:bg-gray-200">
                            <i class="fas fa-times text-gray-500"></i>
                        </button>
    </div>

                    <!-- 智能体快速添加 -->
                    <div class="mb-4">
                        <button onclick="toggleAgentForm()" class="w-full bg-white hover:bg-gray-50 text-gray-700 py-2.5 px-4 rounded-xl border border-gray-200 transition-all flex items-center justify-center group">
                            <i class="fas fa-plus-circle mr-2 text-blue-500 group-hover:scale-110 transition-transform"></i>
                            <span class="font-medium">创建新智能体</span>
                        </button>
                    </div>

                    <!-- 智能体添加表单（默认隐藏） -->
                    <div id="agentForm" class="hidden space-y-3 mb-4 p-4 bg-white rounded-xl border border-gray-100 shadow-sm">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="text-xs font-medium text-gray-600 mb-1 block">智能体名称</label>
                                <input type="text" id="agentName" placeholder="如：数据分析师" 
                                        class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            </div>
                            <div>
                                <label class="text-xs font-medium text-gray-600 mb-1 block">使用模型</label>
                                <select id="agentModel" class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                    <option value="">选择模型</option>
                                    <!-- 动态加载可用模型 -->
                            </select>
                        </div>
                    </div>

                        <div>
                            <label class="text-xs font-medium text-gray-600 mb-1 block">智能体描述</label>
                            <input type="text" id="agentDescription" placeholder="描述智能体的主要功能和用途" 
                                    class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        </div>

                        <div>
                            <label class="text-xs font-medium text-gray-600 mb-1 block">智能体指令</label>
                            <textarea id="agentInstructions" rows="4" placeholder="详细描述智能体的功能、限制和行为方式" 
                                    class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                        </div>
                        
                        <div>
                            <label class="text-xs font-medium text-gray-600 mb-1 block">可用工具</label>
                            <div id="agentToolsContainer" class="mt-2 grid grid-cols-3 gap-2">
                                <!-- 动态加载工具复选框 -->
                    </div>
                </div>

                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <label class="text-xs font-medium text-gray-600">高级设置</label>
                                <button type="button" onclick="toggleAdvancedSettings()" class="text-xs text-blue-600 hover:text-blue-800">
                                    <span id="advancedSettingsText">显示</span> <i id="advancedSettingsIcon" class="fas fa-chevron-down"></i>
                    </button>
                </div>
                            
                            <div id="advancedSettings" class="hidden space-y-3 p-3 bg-gray-50 rounded-lg">
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="text-xs font-medium text-gray-600 mb-1 block">最大思考轮次</label>
                                        <input type="number" id="agentMaxSteps" value="5" min="1" max="10"
                                                class="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                    </div>
                                    <div>
                                        <label class="text-xs font-medium text-gray-600 mb-1 block">温度</label>
                                        <input type="range" id="agentTemperature" min="0" max="100" value="70"
                                                class="w-full h-6 bg-blue-100 rounded-lg appearance-none cursor-pointer">
                                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                                            <span>精确</span>
                                            <span id="tempValue">0.7</span>
                                            <span>创造</span>
                                        </div>
            </div>
        </div>

                                <div>
                                    <label class="text-xs font-medium text-gray-600 mb-1 block">自我反思</label>
                                    <div class="flex items-center mt-1">
                                        <label class="inline-flex items-center cursor-pointer">
                                            <input type="checkbox" id="agentSelfReflection" class="sr-only peer" checked>
                                            <div class="relative w-10 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                                            <span class="ml-2 text-sm font-medium text-gray-700">启用自我反思</span>
                                        </label>
                                        <div class="ml-2 text-xs text-gray-500">
                                            <i class="fas fa-info-circle" data-tooltip="允许智能体评估自己的思考过程并进行修正"></i>
                    </div>
                </div>
            </div>
        </div>
                    </div>
                        
                        <div class="flex space-x-2">
                            <button onclick="createAgent()" class="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-2 px-4 rounded-lg hover:shadow-md transition-all text-sm font-medium">
                                <i class="fas fa-check mr-1"></i>创建智能体
                        </button>
                            <button onclick="toggleAgentForm()" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-all text-sm font-medium">
                                取消
                        </button>
                    </div>
                </div>

                    <!-- 已配置的智能体列表 -->
                    <div id="agentList" class="space-y-2 bg-white rounded-lg p-4 shadow-sm">
                        <!-- 动态加载 -->
                        <div class="text-center p-4 text-gray-500">加载智能体列表...</div>
                </div>
            </div>
            </div>
            
            <!-- 其他内容区域，如主聊天区域、设置、配置等页面 -->
        </div>

        <!-- 输入区域 -->
        <div class="border-t border-gray-200 bg-white">
            <!-- 输入提示栏 -->
            <div id="inputHints" class="px-6 py-2 bg-gray-50 border-b border-gray-100 hidden">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                        <i class="fas fa-lightbulb text-yellow-500"></i>
                        <span>提示：您可以使用 @ 提及MCP工具，使用 / 快速命令</span>
                    </div>
                    <button onclick="hideInputHints()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
            </div>

            <!-- 输入框 -->
            <div class="px-6 py-4">
                <form id="chat-form" class="max-w-4xl mx-auto">
                    <!-- 隐藏的API密钥输入框 -->
                    <input type="hidden" id="currentApiKey" value="">
                    
                    <div class="flex items-end space-x-4">
                        <div class="flex-1">
                            <div class="relative">
                                <textarea id="messageInput" 
                                          placeholder="输入您的消息，按 Enter 发送，Shift+Enter 换行" 
                                          rows="1"
                                          class="w-full px-5 py-3 pr-24 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all"
                                          onkeydown="handleInputKeydown(event)"
                                          oninput="autoResizeTextarea(this)"></textarea>
                                <div class="absolute right-3 bottom-3 flex items-center space-x-2">
                                    <button type="button" onclick="showEmojiPicker()" class="text-gray-400 hover:text-gray-600 p-1">
                                        <i class="far fa-smile"></i>
                                    </button>
                                    <button type="button" onclick="attachFile()" class="text-gray-400 hover:text-gray-600 p-1">
                                        <i class="fas fa-paperclip"></i>
                                    </button>
                                    <div class="w-px h-4 bg-gray-300"></div>
                                    <span id="charCount" class="text-xs text-gray-400">0</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-2">
                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                    <label class="flex items-center cursor-pointer">
                                        <input type="checkbox" id="enterToSend" class="mr-1.5" checked>
                                        <span>Enter 发送</span>
                                    </label>
                                    <span>•</span>
                                    <span id="modelStatus" class="flex items-center">
                                        <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                                        模型就绪
                                    </span>
                                </div>
                                <button id="sendButton" type="submit"
                                        class="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2.5 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg font-medium">
                                    <i class="fas fa-paper-plane mr-2"></i>发送
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 增强的CSS样式 -->
    <style>
        /* 全局字体优化 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f9fafb;
        }
        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* 加载动画 */
        .loading-dots {
            display: inline-flex;
            align-items: center;
        }
        .loading-dots span {
            width: 6px;
            height: 6px;
            margin: 0 2px;
            background-color: #6b7280;
            border-radius: 50%;
            display: inline-block;
            animation: loading-dots 1.4s infinite both;
        }
        .loading-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }
        .loading-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }
        @keyframes loading-dots {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 消息样式优化 */
        .message {
            animation: messageSlideIn 0.3s ease-out;
        }
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            background: white;
            border: 1px solid #e5e7eb;
            border-bottom-left-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        /* 代码块样式 */
        .message pre {
            position: relative;
            background-color: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 0.75rem 0;
        }

        .message pre::before {
            content: attr(data-language);
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            font-size: 0.75rem;
            color: #64748b;
            text-transform: uppercase;
        }

        /* 输入框自动调整高度 */
        #messageInput {
            min-height: 48px;
            max-height: 200px;
        }

        /* 工具提示 */
        [data-tooltip] {
            position: relative;
            cursor: help;
        }
        [data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: #1f2937;
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 50;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* 状态指示器动画 */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
        .status-indicator {
            animation: pulse 2s infinite;
        }

        /* 卡片悬停效果 */
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        /* 按钮点击效果 */
        button:active {
            transform: scale(0.98);
        }

        /* 模型和服务器卡片 */
        .config-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s;
        }
        .config-card:hover {
            border-color: #6366f1;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .w-80 {
                width: 100%;
                position: fixed;
                z-index: 40;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .w-80.open {
                transform: translateX(0);
            }
        }
    </style>

    <script src="/static/js/main.js"></script>
    <script src="/static/js/chat.js"></script>
    <script>
        // 产品级功能增强
        
        // 显示引导
        function showOnboarding() {
            const hasSeenOnboarding = localStorage.getItem('hasSeenOnboarding');
            if (!hasSeenOnboarding) {
                document.getElementById('onboardingOverlay').classList.remove('hidden');
            }
        }

        // 关闭引导
        function closeOnboarding() {
            document.getElementById('onboardingOverlay').classList.add('hidden');
            localStorage.setItem('hasSeenOnboarding', 'true');
        }

        // 切换表单显示
        function toggleModelForm() {
            const form = document.getElementById('modelForm');
            form.classList.toggle('hidden');
        }

        function toggleMCPForm() {
            const form = document.getElementById('mcpForm');
            form.classList.toggle('hidden');
        }

        // 密码可见性切换
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = event.target.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // 自动调整文本框高度
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
            
            // 更新字符计数
            document.getElementById('charCount').textContent = textarea.value.length;
        }

        // 处理输入框快捷键
        function handleInputKeydown(event) {
            const enterToSend = document.getElementById('enterToSend').checked;
            
            if (event.key === 'Enter' && !event.shiftKey && enterToSend) {
                event.preventDefault();
                document.getElementById('chat-form').dispatchEvent(new Event('submit'));
            }
        }

        // 快速提示
        function quickPrompt(prompt) {
            document.getElementById('messageInput').value = prompt;
            document.getElementById('messageInput').focus();
            autoResizeTextarea(document.getElementById('messageInput'));
        }

        // 显示各种弹窗
        function showSystemInfo() {
            alert('系统信息功能开发中...');
        }

        function showQuickStart() {
            alert('快速开始指南开发中...');
        }

        function showHelp() {
            window.open('/docs', '_blank');
        }

        function showSettings() {
            alert('设置功能开发中...');
        }

        function showMCPMarket() {
            alert('MCP工具市场开发中...');
        }

        function showDetailedStats() {
            alert('详细统计功能开发中...');
        }

        function exportChat() {
            alert('导出功能开发中...');
        }

        function shareChat() {
            alert('分享功能开发中...');
        }

        function showEmojiPicker() {
            // 简单的emoji选择
            const emojis = ['😊', '👍', '❤️', '🎉', '🤔', '😂', '🙏', '💡', '🚀', '✨'];
            const emoji = emojis[Math.floor(Math.random() * emojis.length)];
            const input = document.getElementById('messageInput');
            input.value += emoji;
            input.focus();
            autoResizeTextarea(input);
        }

        function attachFile() {
            alert('文件上传功能开发中...');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示引导（首次使用）
            showOnboarding();
            
            // 初始化统计数据
            updateStats();
            
            // 定期更新统计
            setInterval(updateStats, 30000);
        });

        // 更新统计数据
        function updateStats() {
            // 这里应该从后端获取真实数据
            const chatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
            
            const totalChatsElement = document.getElementById('totalChats');
            const totalTokensElement = document.getElementById('totalTokens');
            
            if (totalChatsElement) {
                totalChatsElement.textContent = chatHistory.length;
            }
            
            if (totalTokensElement) {
                // Token计算（示例）
                const totalTokens = chatHistory.reduce((sum, chat) => sum + (chat.tokens || 0), 0);
                totalTokensElement.textContent = formatNumber(totalTokens);
            }
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 更新模型和MCP计数
        function updateCounts() {
            const models = document.querySelectorAll('#modelList .config-card').length;
            const mcpServers = document.querySelectorAll('#mcpList .config-card').length;
            
            document.getElementById('modelCount').textContent = models;
            document.getElementById('mcpCount').textContent = mcpServers;
        }

        // 界面切换函数
        function showMainChat() {
            document.getElementById('mainChatArea').classList.remove('hidden');
            document.getElementById('modelConfigArea').classList.add('hidden');
            document.getElementById('mcpConfigArea').classList.add('hidden');
            document.getElementById('agentConfigArea').classList.add('hidden');
        }

        function showModelConfig() {
            document.getElementById('mainChatArea').classList.add('hidden');
            document.getElementById('modelConfigArea').classList.remove('hidden');
            document.getElementById('mcpConfigArea').classList.add('hidden');
            document.getElementById('agentConfigArea').classList.add('hidden');
            loadModels(); // 加载模型列表
        }

        function showMCPConfig() {
            document.getElementById('mainChatArea').classList.add('hidden');
            document.getElementById('modelConfigArea').classList.add('hidden');
            document.getElementById('mcpConfigArea').classList.remove('hidden');
            document.getElementById('agentConfigArea').classList.add('hidden');
            loadMCPServers(); // 加载MCP服务器列表
        }

        // 初始化时显示主聊天界面
        document.addEventListener('DOMContentLoaded', function() {
            showMainChat();
        });
    </script>
</body>
</html> 
