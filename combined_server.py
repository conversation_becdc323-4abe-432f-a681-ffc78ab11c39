#!/usr/bin/env python
"""
Combined Server - 同时启动AI对话和MCP插件市场
"""
import argparse
import logging
import os
import sys
import threading
import traceback

import uvicorn

# 设置根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, ROOT_DIR)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(ROOT_DIR, 'combined_server.log'))
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Combined Server - 同时启动AI对话和MCP插件市场")
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="监听主机 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--ai-port", 
        type=int, 
        default=8000, 
        help="AI对话端口 (默认: 8000)"
    )
    parser.add_argument(
        "--mcp-port", 
        type=int, 
        default=8001, 
        help="MCP插件市场端口 (默认: 8001)"
    )
    parser.add_argument(
        "--data-dir", 
        default=os.path.join(ROOT_DIR, "mcp_market_data"),
        help="MCP数据目录 (默认: ./mcp_market_data)"
    )
    parser.add_argument(
        "--plugin-port-base", 
        type=int, 
        default=9000,
        help="插件服务端口基数 (默认: 9000)"
    )
    
    return parser.parse_args()

def start_mcp_marketplace(host, port, data_dir, plugin_port_base):
    """启动MCP插件市场"""
    try:
        # 导入MCPMarket类
        try:
            from mcp_market.app import MCPMarket
        except ImportError as e:
            logger.error(f"无法导入MCP Market: {e}")
            logger.error(traceback.format_exc())
            return
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        logger.info(f"启动MCP插件市场")
        logger.info(f"监听地址: {host}:{port}")
        logger.info(f"数据目录: {data_dir}")
        logger.info(f"插件端口基数: {plugin_port_base}")
        
        # 创建并运行应用
        try:
            app = MCPMarket(
                base_dir=data_dir,
                host=host,
                port=port,
                plugin_port_base=plugin_port_base
            )
            
            app.run()
            
        except Exception as e:
            logger.exception(f"启动MCP插件市场时发生错误: {e}")
    except Exception as e:
        logger.exception(f"启动MCP插件市场线程时发生错误: {e}")

def start_ai_chat(host, port):
    """启动AI对话应用"""
    try:
        # 导入AI对话应用
        try:
            from app import app as ai_app
        except ImportError as e:
            logger.error(f"无法导入AI对话应用: {e}")
            logger.error(traceback.format_exc())
            return
        
        logger.info(f"启动AI对话应用")
        logger.info(f"监听地址: {host}:{port}")
        
        # 运行AI对话应用
        uvicorn.run(ai_app, host=host, port=port)
        
    except Exception as e:
        logger.exception(f"启动AI对话应用时发生错误: {e}")

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_args()
        
        logger.info("启动Combined Server - 同时启动AI对话和MCP插件市场")
        
        # 创建MCP插件市场线程
        mcp_thread = threading.Thread(
            target=start_mcp_marketplace,
            args=(args.host, args.mcp_port, args.data_dir, args.plugin_port_base),
            daemon=True
        )
        
        # 启动MCP插件市场线程
        mcp_thread.start()
        
        # 在主线程中启动AI对话应用
        start_ai_chat(args.host, args.ai_port)
        
    except Exception as e:
        logger.exception(f"运行Combined Server时发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
