#!/usr/bin/env python3
"""
应用启动脚本
自动处理数据库初始化和应用启动
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查必要的依赖"""
    print("📦 检查依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "bcrypt",
        "PyJWT"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_").lower())
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("正在安装缺少的依赖...")
        
        for package in missing_packages:
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    print("✅ 依赖检查完成")
    return True

def init_database_if_needed():
    """如果需要则初始化数据库"""
    print("🗄️ 检查数据库...")
    
    # 检查数据库文件是否存在
    db_file = Path("business.db")
    
    if not db_file.exists():
        print("📋 数据库不存在，正在创建...")
        try:
            # 运行数据库初始化
            result = subprocess.run([sys.executable, "init_database.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 数据库初始化成功")
                return True
            else:
                print(f"❌ 数据库初始化失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 数据库初始化异常: {e}")
            return False
    else:
        print("✅ 数据库已存在")
        
        # 检查数据库是否完整
        try:
            from auth_system import auth_service
            from billing_system import billing_service
            
            db = auth_service.get_db()
            try:
                # 尝试查询套餐
                plans = billing_service.get_plans(db)
                if len(plans) == 0:
                    print("⚠️ 数据库为空，正在初始化默认数据...")
                    billing_service._init_default_plans()
                    print("✅ 默认数据初始化完成")
                else:
                    print(f"✅ 数据库完整，共有 {len(plans)} 个套餐")
            finally:
                db.close()
            
            return True
            
        except Exception as e:
            print(f"⚠️ 数据库检查失败: {e}")
            print("尝试重新初始化数据库...")
            
            try:
                result = subprocess.run([sys.executable, "init_database.py"], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ 数据库重新初始化成功")
                    return True
                else:
                    print(f"❌ 数据库重新初始化失败: {result.stderr}")
                    return False
            except Exception as init_e:
                print(f"❌ 数据库重新初始化异常: {init_e}")
                return False

def start_application():
    """启动应用"""
    print("🚀 启动应用...")
    
    try:
        # 检查app.py是否存在
        if not Path("app.py").exists():
            print("❌ app.py 文件不存在")
            return False
        
        # 启动应用
        print("🌐 启动Web服务器...")
        print("📍 应用地址: http://localhost:8000")
        print("📊 企业仪表板: http://localhost:8000/business")
        print("🧠 AI记忆系统: http://localhost:8000/cognee")
        print("\n按 Ctrl+C 停止服务器")
        
        # 使用uvicorn启动
        subprocess.run([sys.executable, "app.py"], check=True)
        
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户停止服务器")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 应用启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False

def show_startup_info():
    """显示启动信息"""
    print("🚀 IntelliHub Pro - 企业级AI工作台")
    print("=" * 50)
    print()
    print("🎯 功能特性:")
    print("✅ 多模型AI聊天")
    print("✅ MCP插件生态")
    print("✅ AI记忆系统 (Cognee)")
    print("✅ 企业级用户管理")
    print("✅ 订阅计费系统")
    print("✅ 实时数据分析")
    print()

def show_access_info():
    """显示访问信息"""
    print("\n🌐 访问地址:")
    print("📱 主页面: http://localhost:8000")
    print("📊 企业仪表板: http://localhost:8000/business")
    print("🧠 AI记忆系统: http://localhost:8000/cognee")
    print("📚 知识库: http://localhost:8000/knowledge-base")
    print("🏪 插件市场: http://localhost:8000/integrated")
    print()
    print("🔑 默认管理员账户:")
    print("  邮箱: <EMAIL>")
    print("  密码: admin123456")
    print("  (请在生产环境中修改密码)")
    print()

def main():
    """主启动流程"""
    show_startup_info()
    
    # 1. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，无法启动")
        sys.exit(1)
    
    # 2. 初始化数据库
    if not init_database_if_needed():
        print("❌ 数据库初始化失败，无法启动")
        sys.exit(1)
    
    # 3. 显示访问信息
    show_access_info()
    
    # 4. 启动应用
    if not start_application():
        print("❌ 应用启动失败")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
        logger.error(f"Startup failed: {e}")
        sys.exit(1)
