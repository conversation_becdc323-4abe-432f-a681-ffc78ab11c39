#!/usr/bin/env python3
"""
商业化功能演示脚本
展示企业级功能的使用方法
"""

import asyncio
import os
import sys
import logging
import json
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demo_auth_system():
    """演示认证系统"""
    print("🔐 认证系统演示")
    print("=" * 50)
    
    try:
        from auth_system import auth_service, UserCreate, UserLogin
        
        # 创建测试用户
        print("📝 创建测试用户...")
        user_data = UserCreate(
            email="<EMAIL>",
            username="demo_user",
            password="demo123456",
            full_name="演示用户"
        )
        
        db = auth_service.get_db()
        try:
            user = auth_service.create_user(user_data, db)
            print(f"✅ 用户创建成功: {user.email}")
        except ValueError as e:
            print(f"⚠️ 用户可能已存在: {e}")
        finally:
            db.close()
        
        # 用户登录
        print("\n🔑 用户登录测试...")
        login_data = UserLogin(
            email="<EMAIL>",
            password="demo123456"
        )
        
        db = auth_service.get_db()
        try:
            user = auth_service.authenticate_user(login_data.email, login_data.password, db)
            if user:
                print(f"✅ 登录成功: {user.username}")
                
                # 生成API密钥
                print("\n🔑 生成API密钥...")
                from auth_system import APIKeyCreate
                key_data = APIKeyCreate(
                    name="演示API密钥",
                    permissions=["chat", "cognee", "mcp"],
                    expires_days=30
                )
                
                # 获取用户的组织
                user_org = db.query(auth_service.UserOrganization).filter(
                    auth_service.UserOrganization.user_id == user.id
                ).first()
                
                if user_org:
                    api_key, key_record = auth_service.generate_api_key(
                        user.id, user_org.organization_id, key_data, db
                    )
                    print(f"✅ API密钥生成成功: {key_record.key_prefix}")
                    print(f"🔐 完整密钥: {api_key}")
                    print("⚠️ 请妥善保存此密钥，它只会显示一次")
            else:
                print("❌ 登录失败")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 认证系统演示失败: {e}")
        return False

async def demo_billing_system():
    """演示计费系统"""
    print("\n💰 计费系统演示")
    print("=" * 50)
    
    try:
        from billing_system import billing_service, SubscriptionCreate, BillingCycle
        
        # 获取所有套餐
        print("📋 可用套餐:")
        db = billing_service.get_db()
        try:
            plans = billing_service.get_plans(db)
            for plan in plans:
                print(f"  📦 {plan.name}")
                print(f"     💰 月付: ¥{plan.monthly_price} | 年付: ¥{plan.yearly_price}")
                print(f"     👥 用户数: {plan.max_users}")
                print(f"     📞 API调用: {plan.max_api_calls_monthly}/月")
                print(f"     💾 存储: {plan.max_storage_gb}GB")
                print()
            
            # 创建订阅（假设有组织ID为1）
            if plans:
                print("🛒 创建订阅演示...")
                subscription_data = SubscriptionCreate(
                    plan_id=plans[1].id if len(plans) > 1 else plans[0].id,  # 选择第二个套餐或第一个
                    billing_cycle=BillingCycle.MONTHLY
                )
                
                try:
                    subscription = billing_service.create_subscription(1, subscription_data, db)
                    print(f"✅ 订阅创建成功: {subscription.plan.name}")
                    print(f"💰 金额: ¥{subscription.amount}")
                    print(f"📅 周期: {subscription.billing_cycle}")
                    print(f"⏰ 到期: {subscription.current_period_end}")
                except Exception as e:
                    print(f"⚠️ 订阅创建失败（可能已存在）: {e}")
            
            # 获取使用统计
            print("\n📊 使用统计:")
            usage_stats = billing_service.get_usage_stats(1, db)
            print(f"  📞 API调用: {usage_stats.api_calls_used}/{usage_stats.api_calls_quota}")
            print(f"  💾 存储: {usage_stats.storage_used_gb:.2f}/{usage_stats.storage_quota_gb}GB")
            print(f"  🤖 模型: {usage_stats.models_used}/{usage_stats.models_quota}")
            
            for resource, percentage in usage_stats.usage_percentage.items():
                print(f"  📈 {resource}: {percentage:.1f}%")
        
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 计费系统演示失败: {e}")
        return False

async def demo_dashboard_system():
    """演示仪表板系统"""
    print("\n📊 仪表板系统演示")
    print("=" * 50)
    
    try:
        from dashboard_system import dashboard_service
        
        db = dashboard_service.get_db()
        try:
            # 获取仪表板统计
            print("📈 仪表板统计:")
            stats = dashboard_service.get_dashboard_stats(db)
            print(f"  👥 总用户数: {stats.total_users}")
            print(f"  🏢 总组织数: {stats.total_organizations}")
            print(f"  📞 今日API调用: {stats.total_api_calls_today}")
            print(f"  📞 本月API调用: {stats.total_api_calls_month}")
            print(f"  💰 本月收入: ¥{stats.revenue_month}")
            
            print("\n🔥 热门功能:")
            for feature in stats.top_features:
                print(f"  📦 {feature['name']}: {feature['usage_count']} 次使用")
            
            # 获取用户分析
            print("\n👥 用户分析:")
            user_analytics = dashboard_service.get_user_analytics(db, 7)  # 最近7天
            print(f"  📈 用户增长数据点: {len(user_analytics.user_growth)}")
            print(f"  📊 用户活跃数据点: {len(user_analytics.user_activity)}")
            print(f"  🔄 用户留存率:")
            for period, rate in user_analytics.user_retention.items():
                print(f"    {period}: {rate:.1f}%")
            
            # 获取收入分析
            print("\n💰 收入分析:")
            revenue_analytics = dashboard_service.get_revenue_analytics(db, 3)  # 最近3个月
            print(f"  📈 收入趋势数据点: {len(revenue_analytics.revenue_trend)}")
            print(f"  📊 套餐分布: {len(revenue_analytics.plan_distribution)} 种套餐")
            print(f"  📉 流失率: {revenue_analytics.churn_rate:.2f}%")
            print(f"  💰 MRR: ¥{revenue_analytics.mrr:.2f}")
            print(f"  💰 ARR: ¥{revenue_analytics.arr:.2f}")
            
            # 获取组织洞察
            print("\n🏢 组织洞察:")
            org_insights = dashboard_service.get_organization_insights(db, 5)
            for org in org_insights:
                print(f"  🏢 {org.name}")
                print(f"     📦 套餐: {org.plan}")
                print(f"     👥 用户数: {org.users_count}")
                print(f"     📞 月度API调用: {org.api_calls_month}")
                print(f"     💾 存储使用: {org.storage_used_gb:.2f}GB")
                print(f"     💯 健康评分: {org.health_score:.1f}/100")
                print()
        
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 仪表板系统演示失败: {e}")
        return False

async def demo_api_usage():
    """演示API使用"""
    print("\n🌐 API使用演示")
    print("=" * 50)
    
    try:
        import requests
        import json
        
        base_url = "http://localhost:8000"
        
        print("📡 测试商业化API端点...")
        
        # 测试仪表板统计API
        try:
            response = requests.get(f"{base_url}/api/business/dashboard/stats", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ 仪表板统计API正常")
                print(f"  📊 数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"⚠️ 仪表板统计API返回: {response.status_code}")
        except requests.exceptions.RequestException:
            print("❌ 无法连接到仪表板统计API")
        
        # 测试套餐API
        try:
            response = requests.get(f"{base_url}/api/business/plans", timeout=5)
            if response.status_code == 200:
                plans = response.json()
                print("✅ 套餐API正常")
                print(f"  📦 套餐数量: {len(plans)}")
            else:
                print(f"⚠️ 套餐API返回: {response.status_code}")
        except requests.exceptions.RequestException:
            print("❌ 无法连接到套餐API")
        
        # 测试用户注册API
        print("\n👤 测试用户注册API...")
        try:
            user_data = {
                "email": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                "username": f"test_user_{datetime.now().strftime('%H%M%S')}",
                "password": "test123456",
                "full_name": "测试用户"
            }
            
            response = requests.post(
                f"{base_url}/api/business/auth/register",
                json=user_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 用户注册API正常")
                print(f"  👤 用户ID: {result.get('user_id')}")
                
                # 测试登录API
                print("\n🔑 测试用户登录API...")
                login_data = {
                    "email": user_data["email"],
                    "password": user_data["password"]
                }
                
                login_response = requests.post(
                    f"{base_url}/api/business/auth/login",
                    json=login_data,
                    timeout=10
                )
                
                if login_response.status_code == 200:
                    tokens = login_response.json()
                    print("✅ 用户登录API正常")
                    print(f"  🔐 访问令牌: {tokens['access_token'][:20]}...")
                else:
                    print(f"⚠️ 用户登录API返回: {login_response.status_code}")
            else:
                print(f"⚠️ 用户注册API返回: {response.status_code}")
                print(f"  错误: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"❌ 用户API测试失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ requests库未安装，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API演示失败: {e}")
        return False

def show_business_info():
    """显示商业化信息"""
    print("💼 IntelliHub Pro - 企业级AI工作台")
    print("=" * 60)
    print()
    print("🎯 商业化功能:")
    print("✅ 用户认证和授权系统")
    print("✅ 多租户组织管理")
    print("✅ 订阅和计费系统")
    print("✅ 使用量统计和配额管理")
    print("✅ 企业级仪表板和分析")
    print("✅ API密钥管理")
    print("✅ 安全审计和日志")
    print()
    print("💰 收入模式:")
    print("📊 订阅制 (SaaS)")
    print("📊 按量付费")
    print("📊 一次性收费")
    print("📊 生态分成")
    print()
    print("🎯 目标市场:")
    print("🏢 中小企业")
    print("👨‍💻 开发团队")
    print("🏛️ 咨询公司")
    print("🎓 教育机构")
    print()

async def main():
    """主演示流程"""
    show_business_info()
    
    # 检查商业化功能是否可用
    try:
        from auth_system import auth_service
        from billing_system import billing_service
        from dashboard_system import dashboard_service
        print("✅ 商业化功能模块加载成功")
    except ImportError as e:
        print(f"❌ 商业化功能不可用: {e}")
        print("请先运行: python install_business_features.py")
        return
    
    print("\n🚀 开始商业化功能演示...")
    
    # 演示各个系统
    success = True
    
    success &= await demo_auth_system()
    success &= await demo_billing_system()
    success &= await demo_dashboard_system()
    success &= await demo_api_usage()
    
    if success:
        print("\n🎉 所有演示完成！")
        print("\n💡 下一步:")
        print("1. 启动应用: python app.py")
        print("2. 访问企业仪表板: http://localhost:8000/business")
        print("3. 开始商业化运营")
    else:
        print("\n⚠️ 部分演示失败，请检查错误信息")

if __name__ == "__main__":
    asyncio.run(main())
