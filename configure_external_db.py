#!/usr/bin/env python3
"""
配置Cognee连接外置数据库
"""

import os
import json
import sys
from pathlib import Path

def configure_chromadb_external():
    """配置外置ChromaDB"""
    print("🔧 配置外置ChromaDB...")
    
    host = input("ChromaDB主机地址 (默认: localhost): ").strip() or "localhost"
    port = input("ChromaDB端口 (默认: 8000): ").strip() or "8000"
    api_key = input("API密钥 (可选): ").strip()
    collection = input("集合名称 (默认: cognee_knowledge): ").strip() or "cognee_knowledge"
    
    config = {
        "vector_db": {
            "provider": "chromadb",
            "config": {
                "host": host,
                "port": int(port),
                "collection_name": collection,
                "distance_function": "cosine"
            }
        }
    }
    
    if api_key:
        config["vector_db"]["config"]["api_key"] = api_key
    
    return config

def configure_pinecone():
    """配置Pinecone"""
    print("🔧 配置Pinecone...")
    
    api_key = input("Pinecone API密钥: ").strip()
    if not api_key:
        print("❌ API密钥是必需的")
        return None
    
    environment = input("环境 (如: us-west1-gcp): ").strip()
    index_name = input("索引名称 (默认: cognee-index): ").strip() or "cognee-index"
    dimension = input("向量维度 (默认: 1536): ").strip() or "1536"
    
    config = {
        "vector_db": {
            "provider": "pinecone",
            "config": {
                "api_key": api_key,
                "environment": environment,
                "index_name": index_name,
                "dimension": int(dimension)
            }
        }
    }
    
    return config

def configure_postgresql():
    """配置PostgreSQL + pgvector"""
    print("🔧 配置PostgreSQL...")
    
    host = input("PostgreSQL主机 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 5432): ").strip() or "5432"
    database = input("数据库名称: ").strip()
    username = input("用户名: ").strip()
    password = input("密码: ").strip()
    table_name = input("表名 (默认: cognee_vectors): ").strip() or "cognee_vectors"
    
    if not all([database, username, password]):
        print("❌ 数据库名称、用户名和密码是必需的")
        return None
    
    config = {
        "vector_db": {
            "provider": "postgresql",
            "config": {
                "host": host,
                "port": int(port),
                "database": database,
                "username": username,
                "password": password,
                "table_name": table_name
            }
        }
    }
    
    return config

def configure_neo4j():
    """配置Neo4j图数据库"""
    print("🔧 配置Neo4j...")
    
    uri = input("Neo4j URI (默认: bolt://localhost:7687): ").strip() or "bolt://localhost:7687"
    username = input("用户名 (默认: neo4j): ").strip() or "neo4j"
    password = input("密码: ").strip()
    database = input("数据库名称 (默认: neo4j): ").strip() or "neo4j"
    
    if not password:
        print("❌ 密码是必需的")
        return None
    
    config = {
        "graph_db": {
            "provider": "neo4j",
            "config": {
                "uri": uri,
                "username": username,
                "password": password,
                "database": database
            }
        }
    }
    
    return config

def configure_weaviate():
    """配置Weaviate"""
    print("🔧 配置Weaviate...")
    
    url = input("Weaviate URL (默认: http://localhost:8080): ").strip() or "http://localhost:8080"
    api_key = input("API密钥 (可选): ").strip()
    class_name = input("类名 (默认: CogneeDocument): ").strip() or "CogneeDocument"
    
    config = {
        "vector_db": {
            "provider": "weaviate",
            "config": {
                "url": url,
                "class_name": class_name
            }
        }
    }
    
    if api_key:
        config["vector_db"]["config"]["api_key"] = api_key
    
    return config

def configure_qdrant():
    """配置Qdrant"""
    print("🔧 配置Qdrant...")
    
    host = input("Qdrant主机 (默认: localhost): ").strip() or "localhost"
    port = input("端口 (默认: 6333): ").strip() or "6333"
    api_key = input("API密钥 (可选): ").strip()
    collection = input("集合名称 (默认: cognee_collection): ").strip() or "cognee_collection"
    
    config = {
        "vector_db": {
            "provider": "qdrant",
            "config": {
                "host": host,
                "port": int(port),
                "collection_name": collection
            }
        }
    }
    
    if api_key:
        config["vector_db"]["config"]["api_key"] = api_key
    
    return config

def save_config(config, config_name):
    """保存配置到文件"""
    config_dir = Path("./cognee_data")
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "database_config.json"
    
    # 如果文件已存在，合并配置
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_config = json.load(f)
        
        # 合并配置
        existing_config.update(config)
        config = existing_config
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置已保存到: {config_file}")
    return config_file

def create_env_file(config):
    """创建环境变量文件"""
    env_content = []
    
    # 向量数据库环境变量
    if "vector_db" in config:
        provider = config["vector_db"]["provider"]
        db_config = config["vector_db"]["config"]
        
        if provider == "chromadb":
            if "host" in db_config:
                env_content.append(f"CHROMA_HOST={db_config['host']}")
                env_content.append(f"CHROMA_PORT={db_config['port']}")
            if "api_key" in db_config:
                env_content.append(f"CHROMA_API_KEY={db_config['api_key']}")
            env_content.append(f"CHROMA_COLLECTION={db_config['collection_name']}")
        
        elif provider == "pinecone":
            env_content.append(f"PINECONE_API_KEY={db_config['api_key']}")
            env_content.append(f"PINECONE_ENVIRONMENT={db_config['environment']}")
            env_content.append(f"PINECONE_INDEX_NAME={db_config['index_name']}")
        
        elif provider == "postgresql":
            env_content.append(f"POSTGRES_HOST={db_config['host']}")
            env_content.append(f"POSTGRES_PORT={db_config['port']}")
            env_content.append(f"POSTGRES_DB={db_config['database']}")
            env_content.append(f"POSTGRES_USER={db_config['username']}")
            env_content.append(f"POSTGRES_PASSWORD={db_config['password']}")
    
    # 图数据库环境变量
    if "graph_db" in config:
        provider = config["graph_db"]["provider"]
        db_config = config["graph_db"]["config"]
        
        if provider == "neo4j":
            env_content.append(f"NEO4J_URI={db_config['uri']}")
            env_content.append(f"NEO4J_USERNAME={db_config['username']}")
            env_content.append(f"NEO4J_PASSWORD={db_config['password']}")
            env_content.append(f"NEO4J_DATABASE={db_config['database']}")
    
    if env_content:
        env_file = Path(".env.cognee_db")
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(env_content))
        
        print(f"✅ 环境变量文件已创建: {env_file}")
        print("请将这些变量添加到你的主.env文件中")

def test_connection(config):
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        if "vector_db" in config:
            provider = config["vector_db"]["provider"]
            print(f"测试向量数据库: {provider}")
            
            if provider == "chromadb":
                import chromadb
                db_config = config["vector_db"]["config"]
                
                if "host" in db_config:
                    # 远程ChromaDB
                    client = chromadb.HttpClient(
                        host=db_config["host"],
                        port=db_config["port"]
                    )
                else:
                    # 本地ChromaDB
                    client = chromadb.PersistentClient(
                        path=db_config.get("persist_directory", "./cognee_data/chroma")
                    )
                
                collections = client.list_collections()
                print(f"✅ ChromaDB连接成功，集合数量: {len(collections)}")
            
            elif provider == "pinecone":
                import pinecone
                db_config = config["vector_db"]["config"]
                
                pinecone.init(
                    api_key=db_config["api_key"],
                    environment=db_config["environment"]
                )
                
                indexes = pinecone.list_indexes()
                print(f"✅ Pinecone连接成功，索引数量: {len(indexes)}")
            
            # 其他数据库的测试可以类似添加
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主配置流程"""
    print("🗄️ Cognee外置数据库配置工具")
    print("=" * 50)
    
    print("\n选择要配置的数据库类型:")
    print("1. ChromaDB (外置)")
    print("2. Pinecone")
    print("3. PostgreSQL + pgvector")
    print("4. Neo4j (图数据库)")
    print("5. Weaviate")
    print("6. Qdrant")
    print("7. 组合配置 (向量DB + 图DB)")
    
    choice = input("\n请选择 (1-7): ").strip()
    
    config = {}
    
    if choice == "1":
        vector_config = configure_chromadb_external()
        if vector_config:
            config.update(vector_config)
    
    elif choice == "2":
        vector_config = configure_pinecone()
        if vector_config:
            config.update(vector_config)
    
    elif choice == "3":
        vector_config = configure_postgresql()
        if vector_config:
            config.update(vector_config)
    
    elif choice == "4":
        graph_config = configure_neo4j()
        if graph_config:
            config.update(graph_config)
    
    elif choice == "5":
        vector_config = configure_weaviate()
        if vector_config:
            config.update(vector_config)
    
    elif choice == "6":
        vector_config = configure_qdrant()
        if vector_config:
            config.update(vector_config)
    
    elif choice == "7":
        print("\n配置向量数据库:")
        vector_choice = input("选择向量DB (1=ChromaDB, 2=Pinecone, 3=PostgreSQL): ")
        
        if vector_choice == "1":
            vector_config = configure_chromadb_external()
        elif vector_choice == "2":
            vector_config = configure_pinecone()
        elif vector_choice == "3":
            vector_config = configure_postgresql()
        else:
            vector_config = None
        
        if vector_config:
            config.update(vector_config)
        
        print("\n配置图数据库:")
        graph_config = configure_neo4j()
        if graph_config:
            config.update(graph_config)
    
    else:
        print("❌ 无效选择")
        return
    
    if not config:
        print("❌ 配置失败")
        return
    
    # 保存配置
    config_file = save_config(config, f"config_{choice}")
    
    # 创建环境变量文件
    create_env_file(config)
    
    # 测试连接
    test_connection(config)
    
    print("\n🎉 配置完成!")
    print("\n📋 下一步:")
    print("1. 重启Cognee服务")
    print("2. 测试数据添加和搜索功能")
    print("3. 检查数据是否正确存储到外置数据库")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户取消配置")
    except Exception as e:
        print(f"\n❌ 配置过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
