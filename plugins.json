{"success": true, "message": null, "data": {"02be270b-994c-4a0a-baac-a442aa1d44df": {"id": "02be270b-994c-4a0a-baac-a442aa1d44df", "name": "bb90233e-739b-4aca-b663-d66ba76dcb3f", "version": "0.1.0", "description": "# AKShare One MCP Server", "status": "installed", "source": "git", "source_url": "https://gitee.com/luoda_123/akshare-mcp.git", "local_path": "C:\\Users\\<USER>\\PycharmProjects2\\run-mcp\\mcp_market_data\\plugins\\bb90233e-739b-4aca-b663-d66ba76dcb3f", "dependency_group": "group_02be270b-994c-4a0a-baac-a442aa1d44df", "entry_point": "main.py", "api_url": null, "error_message": null, "metadata": {"inferred": true}}, "bfe10f02-cc26-4478-b30e-8d539abfe784": {"id": "bfe10f02-cc26-4478-b30e-8d539abfe784", "name": "0741246f-4892-488e-9b04-d66e4976c2be", "version": "0.1.0", "description": "# AKShare One MCP Server", "status": "error", "source": "git", "source_url": "https://gitee.com/luoda_123/akshare-mcp.git", "local_path": "C:\\Users\\<USER>\\PycharmProjects2\\run-mcp\\mcp_market_data\\plugins\\0741246f-4892-488e-9b04-d66e4976c2be", "dependency_group": "group_bfe10f02-cc26-4478-b30e-8d539abfe784", "entry_point": "main.py", "api_url": null, "error_message": "入口文件不存在: C:\\Users\\<USER>\\PycharmProjects2\\run-mcp\\mcp_market_data\\plugins\\0741246f-4892-488e-9b04-d66e4976c2be\\main.py", "metadata": {"inferred": true}}}}