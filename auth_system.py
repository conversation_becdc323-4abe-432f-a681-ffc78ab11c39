"""
企业级用户认证和授权系统
支持多租户、角色权限、API密钥管理
"""

import os
import jwt
import bcrypt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, relationship
from pydantic import BaseModel, EmailStr
import logging

logger = logging.getLogger(__name__)

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./business.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# JWT配置
SECRET_KEY = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# 数据模型
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)
    
    # 关联
    organizations = relationship("UserOrganization", back_populates="user")
    api_keys = relationship("APIKey", back_populates="user")
    usage_records = relationship("UsageRecord", back_populates="user")

class Organization(Base):
    __tablename__ = "organizations"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    slug = Column(String, unique=True, index=True, nullable=False)
    description = Column(Text)
    plan = Column(String, default="free")  # free, pro, enterprise
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 配置
    max_users = Column(Integer, default=5)
    max_api_calls = Column(Integer, default=1000)
    max_storage_gb = Column(Integer, default=1)
    
    # 关联
    users = relationship("UserOrganization", back_populates="organization")
    api_keys = relationship("APIKey", back_populates="organization")
    usage_records = relationship("UsageRecord", back_populates="organization")

class UserOrganization(Base):
    __tablename__ = "user_organizations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    role = Column(String, default="member")  # owner, admin, member, viewer
    joined_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联
    user = relationship("User", back_populates="organizations")
    organization = relationship("Organization", back_populates="users")

class APIKey(Base):
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    key_hash = Column(String, unique=True, index=True, nullable=False)
    key_prefix = Column(String, index=True, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used = Column(DateTime)
    expires_at = Column(DateTime)
    
    # 权限
    permissions = Column(Text)  # JSON格式的权限列表
    
    # 关联
    user = relationship("User", back_populates="api_keys")
    organization = relationship("Organization", back_populates="api_keys")

class UsageRecord(Base):
    __tablename__ = "usage_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    api_key_id = Column(Integer, ForeignKey("api_keys.id"), nullable=True)
    
    # 使用统计
    endpoint = Column(String)
    method = Column(String)
    tokens_used = Column(Integer, default=0)
    cost = Column(Integer, default=0)  # 以分为单位
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 关联
    user = relationship("User", back_populates="usage_records")
    organization = relationship("Organization", back_populates="usage_records")

# Pydantic模型
class UserCreate(BaseModel):
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class UserResponse(BaseModel):
    id: int
    email: str
    username: str
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class OrganizationCreate(BaseModel):
    name: str
    description: Optional[str] = None

class OrganizationResponse(BaseModel):
    id: int
    name: str
    slug: str
    description: Optional[str]
    plan: str
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class APIKeyCreate(BaseModel):
    name: str
    permissions: Optional[List[str]] = None
    expires_days: Optional[int] = None

class APIKeyResponse(BaseModel):
    id: int
    name: str
    key_prefix: str
    is_active: bool
    created_at: datetime
    last_used: Optional[datetime]
    expires_at: Optional[datetime]
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"

# 认证服务类
class AuthService:
    def __init__(self):
        Base.metadata.create_all(bind=engine)
    
    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "type": "access"})
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    def create_refresh_token(self, data: dict):
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode.update({"exp": expire, "type": "refresh"})
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except jwt.PyJWTError:
            return None
    
    def create_user(self, user_data: UserCreate, db: Session) -> User:
        """创建用户"""
        # 检查用户是否已存在
        if db.query(User).filter(User.email == user_data.email).first():
            raise ValueError("邮箱已被注册")
        
        if db.query(User).filter(User.username == user_data.username).first():
            raise ValueError("用户名已被使用")
        
        # 创建用户
        hashed_password = self.hash_password(user_data.password)
        user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=hashed_password,
            full_name=user_data.full_name
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # 创建默认组织
        org = Organization(
            name=f"{user.username}的组织",
            slug=f"{user.username}-org",
            description="默认个人组织"
        )
        db.add(org)
        db.commit()
        db.refresh(org)
        
        # 添加用户到组织
        user_org = UserOrganization(
            user_id=user.id,
            organization_id=org.id,
            role="owner"
        )
        db.add(user_org)
        db.commit()
        
        return user
    
    def authenticate_user(self, email: str, password: str, db: Session) -> Optional[User]:
        """用户认证"""
        user = db.query(User).filter(User.email == email).first()
        if not user or not self.verify_password(password, user.hashed_password):
            return None
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        db.commit()
        
        return user
    
    def generate_api_key(self, user_id: int, org_id: int, key_data: APIKeyCreate, db: Session) -> tuple[str, APIKey]:
        """生成API密钥"""
        # 生成密钥
        api_key = f"ik_{secrets.token_urlsafe(32)}"
        key_hash = bcrypt.hashpw(api_key.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        key_prefix = api_key[:8] + "..."
        
        # 设置过期时间
        expires_at = None
        if key_data.expires_days:
            expires_at = datetime.utcnow() + timedelta(days=key_data.expires_days)
        
        # 创建API密钥记录
        api_key_record = APIKey(
            name=key_data.name,
            key_hash=key_hash,
            key_prefix=key_prefix,
            user_id=user_id,
            organization_id=org_id,
            expires_at=expires_at,
            permissions=str(key_data.permissions or [])
        )
        
        db.add(api_key_record)
        db.commit()
        db.refresh(api_key_record)
        
        return api_key, api_key_record
    
    def verify_api_key(self, api_key: str, db: Session) -> Optional[APIKey]:
        """验证API密钥"""
        # 查找所有活跃的API密钥
        api_keys = db.query(APIKey).filter(APIKey.is_active == True).all()
        
        for key_record in api_keys:
            if bcrypt.checkpw(api_key.encode('utf-8'), key_record.key_hash.encode('utf-8')):
                # 检查是否过期
                if key_record.expires_at and key_record.expires_at < datetime.utcnow():
                    return None
                
                # 更新最后使用时间
                key_record.last_used = datetime.utcnow()
                db.commit()
                
                return key_record
        
        return None
    
    def record_usage(self, user_id: int, org_id: int, endpoint: str, method: str, 
                    tokens_used: int = 0, cost: int = 0, api_key_id: Optional[int] = None, db: Session = None):
        """记录使用情况"""
        if not db:
            db = self.get_db()
        
        usage = UsageRecord(
            user_id=user_id,
            organization_id=org_id,
            api_key_id=api_key_id,
            endpoint=endpoint,
            method=method,
            tokens_used=tokens_used,
            cost=cost
        )
        
        db.add(usage)
        db.commit()

# 全局认证服务实例
auth_service = AuthService()
