#!/usr/bin/env python
"""
Integrated Server - 在一个端口上同时提供AI对话和MCP插件市场
"""
import os
import sys
import argparse
import logging
import uvicorn
import traceback
from pathlib import Path

# 设置根目录
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, ROOT_DIR)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join(ROOT_DIR, 'integrated_server.log'))
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="Integrated Server - 在一个端口上同时提供AI对话和MCP插件市场")
    parser.add_argument(
        "--host", 
        default="0.0.0.0", 
        help="监听主机 (默认: 0.0.0.0)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="服务端口 (默认: 8000)"
    )
    parser.add_argument(
        "--data-dir", 
        default=os.path.join(ROOT_DIR, "mcp_market_data"),
        help="MCP数据目录 (默认: ./mcp_market_data)"
    )
    parser.add_argument(
        "--plugin-port-base", 
        type=int, 
        default=9000,
        help="插件服务端口基数 (默认: 9000)"
    )
    
    return parser.parse_args()

def setup_mcp_plugin_manager(data_dir, plugin_port_base):
    """设置MCP插件管理器"""
    # 确保数据目录存在
    os.makedirs(data_dir, exist_ok=True)
    
    try:
        # 导入MCP市场组件
        from mcp_market.core.plugin_manager import PluginManager
        from mcp_market.core.repository import RepositoryManager
        from mcp_market.core.dependency import DependencyManager
        from mcp_market.services.process_manager import ProcessManager
        
        # 创建组件
        repo_manager = RepositoryManager(
            plugins_dir=str(Path(data_dir) / "plugins")
        )
        
        dep_manager = DependencyManager(
            env_dir=str(Path(data_dir) / "envs"),
            cache_dir=str(Path(data_dir) / "cache")
        )
        
        process_manager = ProcessManager(
            logs_dir=str(Path(data_dir) / "logs"),
            base_port=plugin_port_base
        )
        
        plugin_manager = PluginManager(
            data_dir=str(data_dir),
            repo_manager=repo_manager,
            dep_manager=dep_manager,
            process_manager=process_manager,
            storage_type="sqlite"
        )
        
        return plugin_manager
    except Exception as e:
        logger.exception(f"设置MCP插件管理器时出错: {e}")
        return None

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_args()
        
        logger.info("启动Integrated Server - AI对话和MCP插件市场一体化服务")
        logger.info(f"监听地址: {args.host}:{args.port}")
        logger.info(f"数据目录: {args.data_dir}")
        
        # 1. 先导入原始app.py中的FastAPI应用
        try:
            from app import app
            logger.info("成功导入AI对话应用")
        except Exception as e:
            logger.error(f"无法导入AI对话应用: {e}")
            logger.error(traceback.format_exc())
            sys.exit(1)

        # 2. 设置MCP插件管理器
        plugin_manager = setup_mcp_plugin_manager(args.data_dir, args.plugin_port_base)
        if not plugin_manager:
            logger.warning("MCP插件管理器设置失败，继续使用有限功能")

        # 3. 导入并挂载MCP插件市场API路由到主应用
        try:
            from mcp_market.api.routes import router as mcp_api_router
            from mcp_market.api.routes import get_plugin_manager

            # 3.1. 设置依赖覆盖，使API路由可以访问到我们的插件管理器
            def get_plugin_manager_override():
                return plugin_manager

            app.dependency_overrides[get_plugin_manager] = get_plugin_manager_override

            # 3.2. 挂载MCP API路由到主应用
            app.include_router(mcp_api_router, prefix="/plugins")

            logger.info("成功挂载MCP API路由")
        except Exception as e:
            logger.error(f"挂载MCP API路由时出错: {e}")
            logger.error(traceback.format_exc())

        # # 4. 挂载MCP插件市场静态文件
        # try:
        #     from fastapi.staticfiles import StaticFiles
        #
        #     mcp_static_dir = Path(__file__).parent / "mcp_market/static"
        #     if mcp_static_dir.exists():
        #         app.mount("/mcp-static", StaticFiles(directory=str(mcp_static_dir)), name="mcp-static")
        #         logger.info(f"成功挂载MCP静态文件目录: {mcp_static_dir}")
        # except Exception as e:
        #     logger.error(f"挂载MCP静态文件时出错: {e}")
        #     logger.error(traceback.format_exc())
        #
        # # 5. 启动已安装的MCP插件
        # if plugin_manager:
        #     try:
        #         logger.info("正在启动已安装的自动启动插件...")
        #         autostart_plugins = []
        #
        #         for plugin_id, plugin in plugin_manager.plugins.items():
        #             if plugin.status.value == "installed" and plugin.metadata.get("autostart", False):
        #                 autostart_plugins.append(plugin_id)
        #
        #         logger.info(f"找到 {len(autostart_plugins)} 个需要自动启动的插件")
        #
        #         for plugin_id in autostart_plugins:
        #             try:
        #                 success, error, port = plugin_manager.start_plugin(plugin_id)
        #                 if success:
        #                     logger.info(f"插件 {plugin_id} 自动启动成功，端口: {port}")
        #                 else:
        #                     logger.error(f"插件 {plugin_id} 自动启动失败: {error}")
        #             except Exception as e:
        #                 logger.exception(f"自动启动插件 {plugin_id} 时发生异常: {e}")
        #     except Exception as e:
        #         logger.exception(f"启动已安装插件时出错: {e}")
        
        # 6. 运行集成服务器
        uvicorn.run(app, host=args.host, port=args.port)
        
    except Exception as e:
        logger.exception(f"运行Integrated Server时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
