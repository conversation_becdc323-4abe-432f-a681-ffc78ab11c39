#!/usr/bin/env python3
"""
修复Cognee错误和优化配置
"""

import os
import sys
import json
from pathlib import Path
import asyncio

def analyze_cognee_errors():
    """分析Cognee错误"""
    print("🔍 分析Cognee错误...")
    print("=" * 50)
    
    errors = [
        {
            "error": "EntityNotFoundError: Empty graph projected from the database",
            "cause": "知识图谱为空或Neo4j连接问题",
            "solution": "使用ChromaDB替代Neo4j，或重新生成知识图谱"
        },
        {
            "error": "Ontology file 'None' not found. Using fallback ontology",
            "cause": "本体论文件缺失",
            "solution": "创建默认本体论文件或配置正确的本体论路径"
        }
    ]
    
    for i, error in enumerate(errors, 1):
        print(f"\n❌ 错误 {i}: {error['error']}")
        print(f"🔍 原因: {error['cause']}")
        print(f"💡 解决方案: {error['solution']}")

def create_cognee_config():
    """创建Cognee配置文件"""
    print("\n⚙️ 创建Cognee配置...")
    
    config = {
        "vector_db": {
            "provider": "chromadb",
            "config": {
                "persist_directory": "./cognee_data/chroma",
                "collection_name": "cognee_knowledge"
            }
        },
        "graph_db": {
            "provider": "disabled",  # 禁用Neo4j，只使用ChromaDB
            "config": {}
        },
        "llm": {
            "provider": "openai",
            "model": "gpt-3.5-turbo",
            "temperature": 0.1
        },
        "ontology": {
            "use_default": True,
            "file_path": None
        },
        "processing": {
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "batch_size": 10
        }
    }
    
    config_dir = Path("./cognee_data")
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已创建: {config_file}")
    return config_file

def create_simple_ontology():
    """创建简单的本体论文件"""
    print("\n📚 创建简单本体论...")
    
    ontology_content = """
@prefix : <http://example.org/cognee#> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .

: a owl:Ontology ;
    rdfs:label "Cognee Simple Ontology" ;
    rdfs:comment "A simple ontology for Cognee knowledge management" .

# Classes
:Document a owl:Class ;
    rdfs:label "Document" ;
    rdfs:comment "A document or text content" .

:Entity a owl:Class ;
    rdfs:label "Entity" ;
    rdfs:comment "An entity extracted from text" .

:Concept a owl:Class ;
    rdfs:label "Concept" ;
    rdfs:comment "A concept or idea" .

:Relationship a owl:Class ;
    rdfs:label "Relationship" ;
    rdfs:comment "A relationship between entities" .

# Properties
:hasContent a owl:DatatypeProperty ;
    rdfs:label "has content" ;
    rdfs:domain :Document ;
    rdfs:range rdfs:Literal .

:relatedTo a owl:ObjectProperty ;
    rdfs:label "related to" ;
    rdfs:domain :Entity ;
    rdfs:range :Entity .

:mentions a owl:ObjectProperty ;
    rdfs:label "mentions" ;
    rdfs:domain :Document ;
    rdfs:range :Entity .
"""
    
    ontology_dir = Path("./cognee_data/ontology")
    ontology_dir.mkdir(exist_ok=True)
    
    ontology_file = ontology_dir / "simple_ontology.ttl"
    with open(ontology_file, 'w', encoding='utf-8') as f:
        f.write(ontology_content)
    
    print(f"✅ 本体论文件已创建: {ontology_file}")
    return ontology_file

def setup_chromadb_only():
    """配置仅使用ChromaDB的Cognee"""
    print("\n🗄️ 配置ChromaDB存储...")
    
    try:
        import chromadb
        from chromadb.config import Settings
        
        # 创建ChromaDB客户端
        chroma_dir = Path("./cognee_data/chroma")
        chroma_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置ChromaDB
        client = chromadb.PersistentClient(
            path=str(chroma_dir),
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 创建或获取集合
        collection_name = "cognee_knowledge"
        try:
            collection = client.get_collection(collection_name)
            print(f"✅ 找到现有集合: {collection_name}")
        except:
            collection = client.create_collection(
                name=collection_name,
                metadata={"description": "Cognee knowledge storage"}
            )
            print(f"✅ 创建新集合: {collection_name}")
        
        # 检查集合状态
        count = collection.count()
        print(f"📊 集合中的文档数量: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB配置失败: {e}")
        return False

def test_cognee_with_simple_data():
    """使用简单数据测试Cognee"""
    print("\n🧪 测试Cognee功能...")
    
    async def run_test():
        try:
            from cognee_service import cognee_service
            
            # 检查初始化状态
            if not cognee_service.initialized:
                print("⚠️ Cognee未初始化，尝试初始化...")
                
                # 尝试使用环境变量中的API密钥
                api_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
                if api_key:
                    success = cognee_service.initialize_without_test(api_key)
                    if success:
                        print("✅ Cognee初始化成功")
                    else:
                        print("❌ Cognee初始化失败")
                        return False
                else:
                    print("❌ 未找到API密钥")
                    return False
            
            # 添加简单测试数据
            test_texts = [
                "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
                "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
                "深度学习是机器学习的一种方法，使用神经网络来模拟人脑的工作方式。"
            ]
            
            print("📝 添加测试文本...")
            for i, text in enumerate(test_texts, 1):
                success = await cognee_service.add_text(text, {"source": f"test_{i}"})
                if success:
                    print(f"✅ 文本 {i} 添加成功")
                else:
                    print(f"❌ 文本 {i} 添加失败")
            
            # 尝试生成知识图谱（可能会有错误，但不影响ChromaDB存储）
            print("🧠 尝试生成知识图谱...")
            try:
                cognify_success = await cognee_service.cognify()
                if cognify_success:
                    print("✅ 知识图谱生成成功")
                else:
                    print("⚠️ 知识图谱生成失败，但数据可能已存储在ChromaDB中")
            except Exception as e:
                print(f"⚠️ 知识图谱生成出错: {e}")
                print("这是正常的，数据仍然存储在ChromaDB中")
            
            # 测试搜索功能
            print("🔍 测试搜索功能...")
            try:
                results = await cognee_service.search("人工智能", limit=3)
                print(f"✅ 搜索完成，找到 {len(results)} 个结果")
                
                for i, result in enumerate(results, 1):
                    content = result.get('content', '')[:100]
                    print(f"  {i}. {content}...")
                    
            except Exception as e:
                print(f"⚠️ 搜索功能出错: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    return asyncio.run(run_test())

def check_final_storage():
    """检查最终存储状态"""
    print("\n📊 检查存储状态...")
    
    try:
        import chromadb
        
        # 连接到ChromaDB
        chroma_dir = Path("./cognee_data/chroma")
        if chroma_dir.exists():
            client = chromadb.PersistentClient(path=str(chroma_dir))
            collections = client.list_collections()
            
            print(f"🗄️ ChromaDB状态:")
            print(f"  数据目录: {chroma_dir}")
            print(f"  集合数量: {len(collections)}")
            
            for collection in collections:
                count = collection.count()
                print(f"  📚 {collection.name}: {count} 个文档")
                
                if count > 0:
                    print("✅ 数据已成功存储在ChromaDB中!")
                    return True
        else:
            print("❌ ChromaDB数据目录不存在")
            
    except Exception as e:
        print(f"❌ 检查存储状态失败: {e}")
    
    return False

def main():
    """主修复流程"""
    print("🔧 Cognee错误修复工具")
    print("=" * 50)
    
    # 1. 分析错误
    analyze_cognee_errors()
    
    # 2. 创建配置
    create_cognee_config()
    
    # 3. 创建简单本体论
    create_simple_ontology()
    
    # 4. 配置ChromaDB
    if setup_chromadb_only():
        print("✅ ChromaDB配置成功")
    else:
        print("❌ ChromaDB配置失败")
        return
    
    # 5. 测试功能
    if test_cognee_with_simple_data():
        print("✅ Cognee功能测试完成")
    else:
        print("⚠️ Cognee功能测试有问题，但可能仍然工作")
    
    # 6. 检查最终状态
    if check_final_storage():
        print("\n🎉 修复成功!")
        print("✅ Cognee现在应该能正常存储数据到ChromaDB")
        print("✅ 即使有一些图数据库错误，向量存储仍然工作")
    else:
        print("\n⚠️ 修复部分成功")
        print("请检查API密钥设置并重试")
    
    print("\n💡 使用建议:")
    print("1. 忽略Neo4j/图数据库相关错误")
    print("2. 专注于ChromaDB向量存储功能")
    print("3. 通过Web界面添加文档并搜索测试")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断修复")
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
