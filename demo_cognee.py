#!/usr/bin/env python3
"""
Cognee集成演示脚本
展示如何在项目中使用cognee功能
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def demo_cognee_basic():
    """演示cognee基本功能"""
    print("🚀 Cognee基本功能演示")
    print("=" * 50)
    
    try:
        # 导入cognee服务
        from cognee_service import cognee_service, COGNEE_AVAILABLE
        
        if not COGNEE_AVAILABLE:
            print("❌ Cognee不可用，请先运行: python install_cognee.py")
            return
        
        print("✅ Cognee库可用")
        
        # 检查是否有API密钥
        api_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("⚠️ 未找到API密钥")
            print("请设置环境变量:")
            print("  export LLM_API_KEY='your-openai-api-key'")
            print("或者在Web界面中配置: http://localhost:8000/cognee")
            return
        
        # 初始化cognee服务
        print("🔧 初始化Cognee服务...")
        success = await cognee_service.initialize(api_key, "openai")
        
        if not success:
            print("❌ 初始化失败")
            return
        
        print("✅ 初始化成功")
        
        # 演示添加文本
        print("\n📝 添加示例文本到记忆...")
        sample_texts = [
            "Python是一种高级编程语言，以其简洁和可读性而闻名。",
            "机器学习是人工智能的一个分支，使计算机能够从数据中学习。",
            "FastAPI是一个现代、快速的Web框架，用于构建API。",
            "Cognee是一个为AI代理提供动态记忆的开源系统。"
        ]
        
        for i, text in enumerate(sample_texts, 1):
            success = await cognee_service.add_text(text, {
                "source": "demo",
                "index": i,
                "category": "tech_knowledge"
            })
            if success:
                print(f"  ✅ 文本 {i} 添加成功")
            else:
                print(f"  ❌ 文本 {i} 添加失败")
        
        # 生成知识图谱
        print("\n🧠 生成知识图谱...")
        success = await cognee_service.cognify()
        if success:
            print("✅ 知识图谱生成成功")
        else:
            print("❌ 知识图谱生成失败")
            return
        
        # 演示搜索功能
        print("\n🔍 搜索演示...")
        search_queries = [
            "Python编程",
            "机器学习",
            "API框架",
            "AI记忆"
        ]
        
        for query in search_queries:
            print(f"\n搜索: '{query}'")
            results = await cognee_service.search(query, limit=2)
            
            if results:
                for i, result in enumerate(results, 1):
                    score = int(result['relevance_score'] * 100)
                    content = result['content'][:80] + "..." if len(result['content']) > 80 else result['content']
                    print(f"  结果 {i} ({score}%): {content}")
            else:
                print("  未找到相关结果")
        
        # 演示对话保存
        print("\n💬 对话保存演示...")
        conversations = [
            ("什么是Python？", "Python是一种高级编程语言，以其简洁的语法和强大的功能而著称。"),
            ("如何学习机器学习？", "学习机器学习建议从基础数学开始，然后学习Python编程，最后实践项目。")
        ]
        
        for user_msg, ai_msg in conversations:
            success = await cognee_service.add_conversation(user_msg, ai_msg, "demo_session")
            if success:
                print(f"  ✅ 对话保存成功: {user_msg[:30]}...")
            else:
                print(f"  ❌ 对话保存失败: {user_msg[:30]}...")
        
        # 显示状态
        print("\n📊 服务状态:")
        status = cognee_service.get_status()
        print(f"  可用性: {status['available']}")
        print(f"  已初始化: {status['initialized']}")
        print(f"  数据目录: {status['data_dir']}")
        if status['config']:
            print(f"  LLM提供商: {status['config'].get('llm_provider', 'N/A')}")
        
        print("\n🎉 演示完成！")
        print("\n💡 下一步:")
        print("1. 启动Web应用: python app.py")
        print("2. 访问Cognee界面: http://localhost:8000/cognee")
        print("3. 在聊天中体验自动记忆功能")
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请先安装cognee: python install_cognee.py")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")

async def demo_api_usage():
    """演示API使用方法"""
    print("\n🌐 API使用演示")
    print("=" * 50)
    
    try:
        import requests
        import json
        
        base_url = "http://localhost:8000"
        
        print("📡 测试API端点...")
        
        # 测试状态API
        try:
            response = requests.get(f"{base_url}/api/cognee/status", timeout=5)
            if response.status_code == 200:
                status = response.json()
                print("✅ 状态API正常")
                print(f"  Cognee可用: {status['available']}")
                print(f"  已初始化: {status['initialized']}")
            else:
                print(f"⚠️ 状态API返回: {response.status_code}")
        except requests.exceptions.RequestException:
            print("❌ 无法连接到API")
            print("请确保应用正在运行: python app.py")
            return
        
        # 如果服务已初始化，演示其他API
        if status.get('initialized'):
            print("\n📝 测试添加文本API...")
            text_data = {
                "text": "这是通过API添加的测试文本。",
                "metadata": {
                    "source": "api_demo",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
            }
            
            response = requests.post(
                f"{base_url}/api/cognee/add-text",
                json=text_data,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 文本添加API正常")
            else:
                print(f"⚠️ 文本添加API返回: {response.status_code}")
            
            print("\n🔍 测试搜索API...")
            search_data = {
                "query": "测试",
                "limit": 3
            }
            
            response = requests.post(
                f"{base_url}/api/cognee/search",
                json=search_data,
                timeout=10
            )
            
            if response.status_code == 200:
                results = response.json()
                print("✅ 搜索API正常")
                print(f"  找到 {results.get('count', 0)} 条结果")
            else:
                print(f"⚠️ 搜索API返回: {response.status_code}")
        
        print("\n📚 API文档:")
        print("详细的API文档请参考 COGNEE_INTEGRATION.md")
        
    except ImportError:
        print("❌ requests库未安装")
        print("安装: pip install requests")
    except Exception as e:
        print(f"❌ API演示失败: {e}")

def show_integration_info():
    """显示集成信息"""
    print("🧠 Cognee AI记忆系统集成")
    print("=" * 50)
    print()
    print("📋 集成内容:")
    print("✅ cognee_service.py - Cognee服务封装")
    print("✅ templates/cognee_interface.html - Web界面")
    print("✅ API端点集成到app.py")
    print("✅ 自动对话保存功能")
    print("✅ 安装和测试脚本")
    print()
    print("🚀 快速开始:")
    print("1. 安装依赖: python install_cognee.py")
    print("2. 设置API密钥: export LLM_API_KEY='your-key'")
    print("3. 运行演示: python demo_cognee.py")
    print("4. 启动应用: python app.py")
    print("5. 访问界面: http://localhost:8000/cognee")
    print()
    print("📖 详细文档: COGNEE_INTEGRATION.md")
    print()

async def main():
    """主演示流程"""
    show_integration_info()
    
    # 检查是否有API密钥
    api_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
    
    if api_key:
        print("🔑 找到API密钥，开始完整演示...")
        await demo_cognee_basic()
    else:
        print("⚠️ 未找到API密钥，跳过功能演示")
        print("设置API密钥后重新运行以查看完整演示:")
        print("  export LLM_API_KEY='your-openai-api-key'")
        print("  python demo_cognee.py")
    
    # API演示（不需要API密钥）
    await demo_api_usage()

if __name__ == "__main__":
    asyncio.run(main())
