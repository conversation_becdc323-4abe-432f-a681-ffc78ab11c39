#!/usr/bin/env python3
"""
Cognee外置数据库配置指南
支持的数据库类型和配置方法
"""

import os
import json
from pathlib import Path

def show_supported_databases():
    """显示Cognee支持的数据库类型"""
    print("🗄️ Cognee支持的外置数据库")
    print("=" * 50)
    
    databases = {
        "向量数据库": {
            "ChromaDB": {
                "类型": "本地/云端向量数据库",
                "优势": "简单易用，支持持久化",
                "适用场景": "中小型项目，本地开发",
                "配置复杂度": "⭐"
            },
            "Pinecone": {
                "类型": "云端向量数据库",
                "优势": "高性能，可扩展",
                "适用场景": "生产环境，大规模应用",
                "配置复杂度": "⭐⭐"
            },
            "Weaviate": {
                "类型": "开源向量数据库",
                "优势": "功能丰富，支持多模态",
                "适用场景": "企业级应用",
                "配置复杂度": "⭐⭐⭐"
            },
            "Qdrant": {
                "类型": "高性能向量数据库",
                "优势": "Rust编写，性能优异",
                "适用场景": "高性能需求",
                "配置复杂度": "⭐⭐"
            },
            "Milvus": {
                "类型": "分布式向量数据库",
                "优势": "支持大规模数据",
                "适用场景": "大数据场景",
                "配置复杂度": "⭐⭐⭐⭐"
            }
        },
        "图数据库": {
            "Neo4j": {
                "类型": "图数据库",
                "优势": "成熟稳定，查询语言强大",
                "适用场景": "知识图谱，关系分析",
                "配置复杂度": "⭐⭐⭐"
            },
            "ArangoDB": {
                "类型": "多模型数据库",
                "优势": "支持文档、图、键值",
                "适用场景": "多模型需求",
                "配置复杂度": "⭐⭐⭐"
            },
            "Amazon Neptune": {
                "类型": "云端图数据库",
                "优势": "AWS托管，高可用",
                "适用场景": "AWS环境",
                "配置复杂度": "⭐⭐⭐⭐"
            }
        },
        "关系数据库": {
            "PostgreSQL": {
                "类型": "关系数据库 + 向量扩展",
                "优势": "pgvector扩展支持向量",
                "适用场景": "已有PostgreSQL环境",
                "配置复杂度": "⭐⭐⭐"
            },
            "MySQL": {
                "类型": "关系数据库",
                "优势": "广泛使用，成熟稳定",
                "适用场景": "传统应用集成",
                "配置复杂度": "⭐⭐"
            },
            "SQLite": {
                "类型": "嵌入式数据库",
                "优势": "轻量级，无需服务器",
                "适用场景": "开发测试，小型应用",
                "配置复杂度": "⭐"
            }
        }
    }
    
    for category, dbs in databases.items():
        print(f"\n📊 {category}")
        print("-" * 30)
        
        for name, info in dbs.items():
            print(f"\n🔹 {name}")
            for key, value in info.items():
                print(f"  {key}: {value}")

def create_database_configs():
    """创建各种数据库的配置示例"""
    print("\n⚙️ 数据库配置示例")
    print("=" * 50)
    
    configs = {
        "chromadb_local": {
            "name": "ChromaDB本地配置",
            "config": {
                "vector_db": {
                    "provider": "chromadb",
                    "config": {
                        "persist_directory": "./cognee_data/chroma",
                        "collection_name": "cognee_knowledge",
                        "distance_function": "cosine"
                    }
                }
            }
        },
        "chromadb_cloud": {
            "name": "ChromaDB云端配置",
            "config": {
                "vector_db": {
                    "provider": "chromadb",
                    "config": {
                        "host": "your-chroma-host.com",
                        "port": 8000,
                        "api_key": "your-api-key",
                        "collection_name": "cognee_knowledge"
                    }
                }
            }
        },
        "pinecone": {
            "name": "Pinecone配置",
            "config": {
                "vector_db": {
                    "provider": "pinecone",
                    "config": {
                        "api_key": "your-pinecone-api-key",
                        "environment": "us-west1-gcp",
                        "index_name": "cognee-index",
                        "dimension": 1536
                    }
                }
            }
        },
        "weaviate": {
            "name": "Weaviate配置",
            "config": {
                "vector_db": {
                    "provider": "weaviate",
                    "config": {
                        "url": "http://localhost:8080",
                        "api_key": "your-weaviate-api-key",
                        "class_name": "CogneeDocument"
                    }
                }
            }
        },
        "qdrant": {
            "name": "Qdrant配置",
            "config": {
                "vector_db": {
                    "provider": "qdrant",
                    "config": {
                        "host": "localhost",
                        "port": 6333,
                        "api_key": "your-qdrant-api-key",
                        "collection_name": "cognee_collection"
                    }
                }
            }
        },
        "neo4j": {
            "name": "Neo4j图数据库配置",
            "config": {
                "graph_db": {
                    "provider": "neo4j",
                    "config": {
                        "uri": "bolt://localhost:7687",
                        "username": "neo4j",
                        "password": "your-password",
                        "database": "cognee"
                    }
                }
            }
        },
        "postgresql": {
            "name": "PostgreSQL + pgvector配置",
            "config": {
                "vector_db": {
                    "provider": "postgresql",
                    "config": {
                        "host": "localhost",
                        "port": 5432,
                        "database": "cognee_db",
                        "username": "cognee_user",
                        "password": "your-password",
                        "table_name": "cognee_vectors"
                    }
                }
            }
        },
        "combined": {
            "name": "组合配置（向量+图数据库）",
            "config": {
                "vector_db": {
                    "provider": "pinecone",
                    "config": {
                        "api_key": "your-pinecone-api-key",
                        "environment": "us-west1-gcp",
                        "index_name": "cognee-vectors"
                    }
                },
                "graph_db": {
                    "provider": "neo4j",
                    "config": {
                        "uri": "bolt://localhost:7687",
                        "username": "neo4j",
                        "password": "your-password"
                    }
                }
            }
        }
    }
    
    config_dir = Path("./cognee_configs")
    config_dir.mkdir(exist_ok=True)
    
    for config_name, config_data in configs.items():
        print(f"\n📝 {config_data['name']}")
        
        config_file = config_dir / f"{config_name}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data['config'], f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件已创建: {config_file}")
        
        # 显示配置内容
        print("配置内容:")
        print(json.dumps(config_data['config'], indent=2, ensure_ascii=False))

def create_environment_templates():
    """创建环境变量模板"""
    print("\n🔑 环境变量配置模板")
    print("=" * 50)
    
    env_templates = {
        ".env.chromadb": """# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8000
CHROMA_API_KEY=your-api-key
CHROMA_COLLECTION=cognee_knowledge
""",
        ".env.pinecone": """# Pinecone配置
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=cognee-index
""",
        ".env.neo4j": """# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-password
NEO4J_DATABASE=cognee
""",
        ".env.postgresql": """# PostgreSQL配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=cognee_db
POSTGRES_USER=cognee_user
POSTGRES_PASSWORD=your-password
""",
        ".env.weaviate": """# Weaviate配置
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key
WEAVIATE_CLASS_NAME=CogneeDocument
""",
        ".env.qdrant": """# Qdrant配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=your-qdrant-api-key
QDRANT_COLLECTION=cognee_collection
"""
    }
    
    for filename, content in env_templates.items():
        print(f"\n📄 {filename}")
        print(content)
        
        # 保存到文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 模板已保存: {filename}")

def show_setup_instructions():
    """显示设置说明"""
    print("\n📚 数据库设置说明")
    print("=" * 50)
    
    instructions = {
        "ChromaDB": {
            "安装": "pip install chromadb",
            "启动": "自动启动（嵌入式）或 docker run -p 8000:8000 chromadb/chroma",
            "配置": "修改cognee配置文件指向ChromaDB"
        },
        "Pinecone": {
            "安装": "pip install pinecone-client",
            "启动": "注册Pinecone账户，获取API密钥",
            "配置": "设置PINECONE_API_KEY环境变量"
        },
        "Neo4j": {
            "安装": "下载Neo4j Desktop或使用Docker",
            "启动": "docker run -p 7474:7474 -p 7687:7687 neo4j",
            "配置": "设置用户名密码，配置连接URI"
        },
        "PostgreSQL": {
            "安装": "安装PostgreSQL + pgvector扩展",
            "启动": "启动PostgreSQL服务",
            "配置": "CREATE EXTENSION vector; 创建数据库和用户"
        },
        "Weaviate": {
            "安装": "docker run -p 8080:8080 semitechnologies/weaviate",
            "启动": "Docker容器启动",
            "配置": "配置schema和API密钥"
        }
    }
    
    for db_name, steps in instructions.items():
        print(f"\n🔹 {db_name} 设置步骤:")
        for step_name, instruction in steps.items():
            print(f"  {step_name}: {instruction}")

def main():
    """主函数"""
    print("🗄️ Cognee外置数据库配置指南")
    print("=" * 60)
    
    # 1. 显示支持的数据库
    show_supported_databases()
    
    # 2. 创建配置示例
    create_database_configs()
    
    # 3. 创建环境变量模板
    create_environment_templates()
    
    # 4. 显示设置说明
    show_setup_instructions()
    
    print("\n💡 推荐配置:")
    print("🥇 开发环境: ChromaDB (本地)")
    print("🥈 生产环境: Pinecone + Neo4j")
    print("🥉 企业环境: PostgreSQL + Weaviate")
    
    print("\n📋 下一步:")
    print("1. 选择适合的数据库组合")
    print("2. 使用对应的配置文件")
    print("3. 设置环境变量")
    print("4. 重启Cognee服务")

if __name__ == "__main__":
    main()
