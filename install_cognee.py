#!/usr/bin/env python3
"""
Cognee集成安装脚本
自动安装cognee及其依赖项
"""

import subprocess
import sys
import os
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """运行命令并处理错误"""
    try:
        logger.info(f"执行: {description or command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"命令执行失败: {command}")
        logger.error(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info("✓ Python版本检查通过")
    return True

def check_pip():
    """检查pip是否可用"""
    try:
        import pip
        logger.info("✓ pip可用")
        return True
    except ImportError:
        logger.error("pip不可用，请先安装pip")
        return False

def install_package(package_name, description=""):
    """安装Python包"""
    logger.info(f"安装 {package_name}...")
    
    # 尝试使用pip安装
    command = f"{sys.executable} -m pip install {package_name}"
    
    if run_command(command, f"安装{description or package_name}"):
        logger.info(f"✓ {package_name} 安装成功")
        return True
    else:
        logger.error(f"✗ {package_name} 安装失败")
        return False

def install_cognee_dependencies():
    """安装cognee及其依赖"""
    dependencies = [
        ("cognee>=0.1.42", "Cognee AI记忆系统"),
        ("neo4j>=5.0.0", "Neo4j图数据库驱动"),
        ("chromadb>=0.4.0", "ChromaDB向量数据库"),
        ("sentence-transformers>=2.2.0", "句子转换器"),
        ("networkx>=3.0", "网络分析库"),
    ]
    
    failed_packages = []
    
    for package, description in dependencies:
        if not install_package(package, description):
            failed_packages.append(package)
    
    return failed_packages

def test_cognee_import():
    """测试cognee是否可以正常导入"""
    try:
        import cognee
        logger.info("✓ Cognee导入测试成功")
        return True
    except ImportError as e:
        logger.error(f"✗ Cognee导入失败: {e}")
        return False

def create_cognee_data_dir():
    """创建cognee数据目录"""
    data_dir = Path("./cognee_data")
    try:
        data_dir.mkdir(exist_ok=True)
        logger.info(f"✓ 创建数据目录: {data_dir.absolute()}")
        return True
    except Exception as e:
        logger.error(f"✗ 创建数据目录失败: {e}")
        return False

def update_requirements_file():
    """更新requirements.txt文件"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        logger.warning("requirements.txt文件不存在，跳过更新")
        return True
    
    try:
        # 读取现有内容
        with open(requirements_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含cognee依赖
        if "cognee" in content:
            logger.info("✓ requirements.txt已包含cognee依赖")
            return True
        
        # 添加cognee依赖
        cognee_deps = """
# Cognee - AI Memory System
cognee>=0.1.42

# Additional dependencies for cognee
neo4j>=5.0.0
chromadb>=0.4.0
sentence-transformers>=2.2.0
networkx>=3.0
"""
        
        with open(requirements_file, 'a', encoding='utf-8') as f:
            f.write(cognee_deps)
        
        logger.info("✓ 已更新requirements.txt文件")
        return True
        
    except Exception as e:
        logger.error(f"✗ 更新requirements.txt失败: {e}")
        return False

def main():
    """主安装流程"""
    logger.info("开始安装Cognee AI记忆系统...")
    
    # 检查环境
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # 创建数据目录
    if not create_cognee_data_dir():
        logger.warning("数据目录创建失败，但可以继续安装")
    
    # 安装依赖
    failed_packages = install_cognee_dependencies()
    
    if failed_packages:
        logger.error(f"以下包安装失败: {', '.join(failed_packages)}")
        logger.error("请手动安装这些包或检查网络连接")
        
        # 提供手动安装命令
        logger.info("手动安装命令:")
        for package in failed_packages:
            logger.info(f"  pip install {package}")
        
        sys.exit(1)
    
    # 测试导入
    if not test_cognee_import():
        logger.error("Cognee安装可能有问题，请检查错误信息")
        sys.exit(1)
    
    # 更新requirements文件
    update_requirements_file()
    
    logger.info("🎉 Cognee AI记忆系统安装完成!")
    logger.info("")
    logger.info("下一步:")
    logger.info("1. 启动应用: python app.py")
    logger.info("2. 访问 http://localhost:8000/cognee")
    logger.info("3. 配置API密钥并开始使用")
    logger.info("")
    logger.info("注意事项:")
    logger.info("- 首次使用需要配置LLM API密钥")
    logger.info("- 建议使用OpenAI或Anthropic的API")
    logger.info("- 数据将保存在 ./cognee_data 目录中")

if __name__ == "__main__":
    main()
