<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cognee AI记忆系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cognee-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .status-card {
            border-left: 4px solid #28a745;
            background: #f8f9fa;
        }
        .status-card.error {
            border-left-color: #dc3545;
        }
        .search-results {
            max-height: 400px;
            overflow-y: auto;
        }
        .result-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
        }
        .relevance-score {
            background: #007bff;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="cognee-header">
        <div class="container">
            <h1><i class="fas fa-brain"></i> Cognee AI记忆系统</h1>
            <p class="mb-0">为AI代理构建动态记忆和知识图谱</p>
        </div>
    </div>

    <div class="container mt-4">
        <!-- 状态面板 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card status-card" id="statusCard">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-info-circle"></i> 系统状态</h5>
                        <div id="statusContent">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            正在检查状态...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 初始化面板 -->
        <div class="row mb-4" id="initPanel" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> 初始化Cognee服务</h5>
                    </div>
                    <div class="card-body">
                        <form id="initForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="apiKey" class="form-label">API密钥</label>
                                    <input type="password" class="form-control" id="apiKey" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="provider" class="form-label">提供商</label>
                                    <select class="form-select" id="provider">
                                        <option value="openai">OpenAI</option>
                                        <option value="anthropic">Anthropic</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="skipTest">
                                        <label class="form-check-label" for="skipTest">
                                            跳过功能测试
                                        </label>
                                    </div>
                                    <button type="submit" class="btn btn-primary d-block w-100">
                                        <i class="fas fa-play"></i> 初始化
                                    </button>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        如果初始化失败，请尝试勾选"跳过功能测试"选项
                                    </small>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主功能面板 -->
        <div class="row" id="mainPanel" style="display: none;">
            <!-- 左侧：添加内容 -->
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> 添加内容到记忆</h5>
                    </div>
                    <div class="card-body">
                        <!-- 文本输入 -->
                        <div class="mb-3">
                            <label for="textInput" class="form-label">添加文本</label>
                            <textarea class="form-control" id="textInput" rows="4" placeholder="输入要添加到记忆中的文本..."></textarea>
                            <button class="btn btn-success mt-2" onclick="addText()">
                                <i class="fas fa-plus"></i> 添加文本
                            </button>
                        </div>

                        <!-- 文件上传 -->
                        <div class="mb-3">
                            <label class="form-label">上传文件</label>
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p>拖拽文件到这里或点击选择文件</p>
                                <input type="file" id="fileInput" class="d-none" multiple accept=".txt,.md,.pdf,.docx">
                                <button class="btn btn-outline-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-file"></i> 选择文件
                                </button>
                            </div>
                        </div>

                        <!-- Cognify按钮 -->
                        <div class="text-center">
                            <button class="btn btn-warning btn-lg" onclick="cognify()">
                                <i class="fas fa-magic"></i> 生成知识图谱 (Cognify)
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：搜索和结果 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-search"></i> 搜索记忆</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="searchInput" placeholder="搜索记忆中的内容...">
                            <button class="btn btn-primary" onclick="searchMemory()">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>

                        <div id="searchResults" class="search-results">
                            <div class="text-center text-muted">
                                <i class="fas fa-search fa-2x mb-2"></i>
                                <p>输入搜索词开始搜索记忆</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mt-4" id="actionPanel" style="display: none;">
            <div class="col-12 text-center">
                <button class="btn btn-danger" onclick="resetCognee()">
                    <i class="fas fa-trash"></i> 重置记忆数据
                </button>
                <button class="btn btn-info ms-2" onclick="checkStatus()">
                    <i class="fas fa-refresh"></i> 刷新状态
                </button>
            </div>
        </div>
    </div>

    <!-- Toast通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <strong class="me-auto">Cognee</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toastBody">
                消息内容
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            setupFileUpload();
        });

        // 检查Cognee状态
        async function checkStatus() {
            try {
                const response = await fetch('/api/cognee/status');
                const status = await response.json();
                
                const statusCard = document.getElementById('statusCard');
                const statusContent = document.getElementById('statusContent');
                const initPanel = document.getElementById('initPanel');
                const mainPanel = document.getElementById('mainPanel');
                const actionPanel = document.getElementById('actionPanel');
                
                if (status.available && status.initialized) {
                    statusCard.className = 'card status-card';
                    statusContent.innerHTML = `
                        <i class="fas fa-check-circle text-success"></i>
                        <strong>Cognee服务运行正常</strong><br>
                        <small>提供商: ${status.config?.llm_provider || 'N/A'} | 
                        数据目录: ${status.data_dir}</small>
                    `;
                    initPanel.style.display = 'none';
                    mainPanel.style.display = 'block';
                    actionPanel.style.display = 'block';
                } else if (status.available && !status.initialized) {
                    statusCard.className = 'card status-card error';
                    statusContent.innerHTML = `
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        <strong>Cognee可用但未初始化</strong><br>
                        <small>请提供API密钥进行初始化</small>
                    `;
                    initPanel.style.display = 'block';
                    mainPanel.style.display = 'none';
                    actionPanel.style.display = 'none';
                } else {
                    statusCard.className = 'card status-card error';
                    statusContent.innerHTML = `
                        <i class="fas fa-times-circle text-danger"></i>
                        <strong>Cognee不可用</strong><br>
                        <small>请确保已安装cognee库</small>
                    `;
                    initPanel.style.display = 'none';
                    mainPanel.style.display = 'none';
                    actionPanel.style.display = 'none';
                }
            } catch (error) {
                console.error('检查状态失败:', error);
                showToast('检查状态失败: ' + error.message, 'error');
            }
        }

        // 初始化Cognee服务
        async function initializeCognee() {
            const apiKey = document.getElementById('apiKey').value;
            const provider = document.getElementById('provider').value;
            const skipTest = document.getElementById('skipTest').checked;

            if (!apiKey) {
                showToast('请输入API密钥', 'error');
                return;
            }

            try {
                showToast('正在初始化Cognee服务...', 'info');

                const response = await fetch('/api/cognee/initialize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        api_key: apiKey,
                        provider: provider,
                        skip_test: skipTest
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    let message = result.message;

                    if (result.warning) {
                        message += ' (' + result.warning + ')';
                        showToast(message, 'warning');
                    } else {
                        showToast(message, 'success');
                    }

                    checkStatus();
                } else {
                    const error = await response.json();
                    showToast('初始化失败: ' + error.detail, 'error');
                }
            } catch (error) {
                console.error('初始化失败:', error);
                showToast('初始化失败: ' + error.message, 'error');
            }
        }

        // 添加文本
        async function addText() {
            const text = document.getElementById('textInput').value;
            
            if (!text.trim()) {
                showToast('请输入文本内容', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/cognee/add-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        metadata: {
                            source: 'web_interface',
                            timestamp: new Date().toISOString()
                        }
                    })
                });
                
                if (response.ok) {
                    showToast('文本添加成功', 'success');
                    document.getElementById('textInput').value = '';
                } else {
                    const error = await response.json();
                    showToast('添加失败: ' + error.detail, 'error');
                }
            } catch (error) {
                console.error('添加文本失败:', error);
                showToast('添加文本失败: ' + error.message, 'error');
            }
        }

        // 搜索记忆
        async function searchMemory() {
            const query = document.getElementById('searchInput').value;
            
            if (!query.trim()) {
                showToast('请输入搜索词', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/cognee/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        limit: 5
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displaySearchResults(data.results);
                } else {
                    const error = await response.json();
                    showToast('搜索失败: ' + error.detail, 'error');
                }
            } catch (error) {
                console.error('搜索失败:', error);
                showToast('搜索失败: ' + error.message, 'error');
            }
        }

        // 显示搜索结果
        function displaySearchResults(results) {
            const container = document.getElementById('searchResults');
            
            if (results.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <p>未找到相关结果</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = results.map(result => `
                <div class="result-item">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <span class="relevance-score">${(result.relevance_score * 100).toFixed(0)}% 相关</span>
                        <small class="text-muted">${new Date(result.timestamp).toLocaleString()}</small>
                    </div>
                    <p class="mb-0">${result.content}</p>
                </div>
            `).join('');
        }

        // 生成知识图谱
        async function cognify() {
            try {
                showToast('正在生成知识图谱...', 'info');
                
                const response = await fetch('/api/cognee/cognify', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    showToast('知识图谱生成成功', 'success');
                } else {
                    const error = await response.json();
                    showToast('生成失败: ' + error.detail, 'error');
                }
            } catch (error) {
                console.error('生成知识图谱失败:', error);
                showToast('生成知识图谱失败: ' + error.message, 'error');
            }
        }

        // 重置Cognee数据
        async function resetCognee() {
            if (!confirm('确定要重置所有Cognee数据吗？此操作不可撤销。')) {
                return;
            }
            
            try {
                const response = await fetch('/api/cognee/reset', {
                    method: 'POST'
                });
                
                if (response.ok) {
                    showToast('数据重置成功', 'success');
                    checkStatus();
                } else {
                    const error = await response.json();
                    showToast('重置失败: ' + error.detail, 'error');
                }
            } catch (error) {
                console.error('重置失败:', error);
                showToast('重置失败: ' + error.message, 'error');
            }
        }

        // 设置文件上传
        function setupFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });
            
            // 文件选择事件
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
        }

        // 处理文件上传
        async function handleFiles(files) {
            for (let file of files) {
                try {
                    const formData = new FormData();
                    formData.append('file', file);
                    
                    showToast(`正在上传 ${file.name}...`, 'info');
                    
                    const response = await fetch('/api/cognee/add-file', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (response.ok) {
                        showToast(`${file.name} 上传成功`, 'success');
                    } else {
                        const error = await response.json();
                        showToast(`${file.name} 上传失败: ${error.detail}`, 'error');
                    }
                } catch (error) {
                    console.error('文件上传失败:', error);
                    showToast(`${file.name} 上传失败: ${error.message}`, 'error');
                }
            }
        }

        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = document.getElementById('toastBody');
            
            toastBody.textContent = message;
            
            // 设置Toast样式
            toast.className = 'toast';
            if (type === 'success') {
                toast.classList.add('bg-success', 'text-white');
            } else if (type === 'error') {
                toast.classList.add('bg-danger', 'text-white');
            } else if (type === 'warning') {
                toast.classList.add('bg-warning');
            } else {
                toast.classList.add('bg-info', 'text-white');
            }
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }

        // 表单提交事件
        document.getElementById('initForm').addEventListener('submit', function(e) {
            e.preventDefault();
            initializeCognee();
        });

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchMemory();
            }
        });
    </script>
</body>
</html>
