"""
企业级计费和订阅管理系统
支持多种定价模式、使用量统计、发票生成
"""

import os
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
from enum import Enum
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Numeric
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from pydantic import BaseModel
import logging

from auth_system import Base, SessionLocal

logger = logging.getLogger(__name__)

# 枚举类型
class PlanType(str, Enum):
    FREE = "free"
    PERSONAL = "personal"
    TEAM = "team"
    ENTERPRISE = "enterprise"

class BillingCycle(str, Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"

class InvoiceStatus(str, Enum):
    DRAFT = "draft"
    PENDING = "pending"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"

# 数据模型
class Plan(Base):
    __tablename__ = "plans"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    type = Column(String, nullable=False)  # PlanType
    description = Column(Text)
    
    # 定价
    monthly_price = Column(Numeric(10, 2), default=0)
    yearly_price = Column(Numeric(10, 2), default=0)
    
    # 限制
    max_users = Column(Integer, default=1)
    max_api_calls_monthly = Column(Integer, default=1000)
    max_storage_gb = Column(Integer, default=1)
    max_models = Column(Integer, default=1)
    max_mcp_servers = Column(Integer, default=1)
    
    # 功能
    features = Column(Text)  # JSON格式的功能列表
    
    # 状态
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联
    subscriptions = relationship("Subscription", back_populates="plan")

class Subscription(Base):
    __tablename__ = "subscriptions"
    
    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    plan_id = Column(Integer, ForeignKey("plans.id"))
    
    # 订阅信息
    billing_cycle = Column(String, default="monthly")  # BillingCycle
    status = Column(String, default="active")  # active, cancelled, expired
    
    # 时间
    started_at = Column(DateTime, default=datetime.utcnow)
    current_period_start = Column(DateTime, default=datetime.utcnow)
    current_period_end = Column(DateTime)
    cancelled_at = Column(DateTime)
    
    # 定价
    amount = Column(Numeric(10, 2))
    currency = Column(String, default="CNY")
    
    # 关联
    plan = relationship("Plan", back_populates="subscriptions")
    invoices = relationship("Invoice", back_populates="subscription")

class Invoice(Base):
    __tablename__ = "invoices"
    
    id = Column(Integer, primary_key=True, index=True)
    subscription_id = Column(Integer, ForeignKey("subscriptions.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    
    # 发票信息
    invoice_number = Column(String, unique=True, nullable=False)
    status = Column(String, default="draft")  # InvoiceStatus
    
    # 金额
    subtotal = Column(Numeric(10, 2))
    tax_amount = Column(Numeric(10, 2), default=0)
    total_amount = Column(Numeric(10, 2))
    currency = Column(String, default="CNY")
    
    # 时间
    issue_date = Column(DateTime, default=datetime.utcnow)
    due_date = Column(DateTime)
    paid_at = Column(DateTime)
    
    # 详情
    line_items = Column(Text)  # JSON格式的明细
    notes = Column(Text)
    
    # 关联
    subscription = relationship("Subscription", back_populates="invoices")

class UsageQuota(Base):
    __tablename__ = "usage_quotas"
    
    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    
    # 配额
    api_calls_quota = Column(Integer, default=1000)
    storage_quota_gb = Column(Integer, default=1)
    models_quota = Column(Integer, default=1)
    
    # 当前使用量
    api_calls_used = Column(Integer, default=0)
    storage_used_gb = Column(Numeric(10, 3), default=0)
    models_used = Column(Integer, default=0)
    
    # 重置时间
    reset_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

# Pydantic模型
class PlanResponse(BaseModel):
    id: int
    name: str
    type: str
    description: Optional[str]
    monthly_price: float
    yearly_price: float
    max_users: int
    max_api_calls_monthly: int
    max_storage_gb: int
    max_models: int
    max_mcp_servers: int
    features: List[str]
    is_active: bool
    
    class Config:
        from_attributes = True

class SubscriptionCreate(BaseModel):
    plan_id: int
    billing_cycle: BillingCycle = BillingCycle.MONTHLY

class SubscriptionResponse(BaseModel):
    id: int
    plan: PlanResponse
    billing_cycle: str
    status: str
    started_at: datetime
    current_period_start: datetime
    current_period_end: Optional[datetime]
    amount: float
    currency: str
    
    class Config:
        from_attributes = True

class UsageStats(BaseModel):
    api_calls_used: int
    api_calls_quota: int
    storage_used_gb: float
    storage_quota_gb: int
    models_used: int
    models_quota: int
    usage_percentage: Dict[str, float]

# 计费服务类
class BillingService:
    def __init__(self):
        self._initialized = False
    
    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            try:
                # 检查数据库表是否存在
                db = self.get_db()
                try:
                    # 尝试查询套餐表
                    db.query(Plan).count()
                    # 如果查询成功但没有数据，则初始化默认套餐
                    if db.query(Plan).count() == 0:
                        self._init_default_plans()
                    self._initialized = True
                except Exception:
                    # 表不存在或其他错误，跳过初始化
                    pass
                finally:
                    db.close()
            except Exception:
                # 初始化失败，跳过
                pass
    
    def _init_default_plans(self):
        """初始化默认套餐"""
        db = self.get_db()
        
        # 检查是否已有套餐
        if db.query(Plan).count() > 0:
            return
        
        default_plans = [
            {
                "name": "免费版",
                "type": PlanType.FREE,
                "description": "适合个人用户和小型项目",
                "monthly_price": 0,
                "yearly_price": 0,
                "max_users": 1,
                "max_api_calls_monthly": 1000,
                "max_storage_gb": 1,
                "max_models": 1,
                "max_mcp_servers": 1,
                "features": ["基础聊天", "1个AI模型", "基础记忆", "社区支持"]
            },
            {
                "name": "个人版",
                "type": PlanType.PERSONAL,
                "description": "适合个人开发者和小团队",
                "monthly_price": 99,
                "yearly_price": 999,
                "max_users": 3,
                "max_api_calls_monthly": 10000,
                "max_storage_gb": 10,
                "max_models": 5,
                "max_mcp_servers": 5,
                "features": ["多模型支持", "高级记忆", "MCP插件", "API访问", "邮件支持"]
            },
            {
                "name": "团队版",
                "type": PlanType.TEAM,
                "description": "适合中小团队和企业",
                "monthly_price": 299,
                "yearly_price": 2999,
                "max_users": 10,
                "max_api_calls_monthly": 50000,
                "max_storage_gb": 50,
                "max_models": 20,
                "max_mcp_servers": 20,
                "features": ["团队协作", "权限管理", "数据分析", "优先支持", "定制集成"]
            },
            {
                "name": "企业版",
                "type": PlanType.ENTERPRISE,
                "description": "适合大型企业和组织",
                "monthly_price": 999,
                "yearly_price": 9999,
                "max_users": 100,
                "max_api_calls_monthly": 500000,
                "max_storage_gb": 500,
                "max_models": 100,
                "max_mcp_servers": 100,
                "features": ["私有部署", "SSO集成", "审计日志", "24/7支持", "专属客服", "定制开发"]
            }
        ]
        
        for plan_data in default_plans:
            plan = Plan(
                name=plan_data["name"],
                type=plan_data["type"],
                description=plan_data["description"],
                monthly_price=plan_data["monthly_price"],
                yearly_price=plan_data["yearly_price"],
                max_users=plan_data["max_users"],
                max_api_calls_monthly=plan_data["max_api_calls_monthly"],
                max_storage_gb=plan_data["max_storage_gb"],
                max_models=plan_data["max_models"],
                max_mcp_servers=plan_data["max_mcp_servers"],
                features=json.dumps(plan_data["features"], ensure_ascii=False)
            )
            db.add(plan)
        
        db.commit()
    
    def get_plans(self, db: Session) -> List[Plan]:
        """获取所有可用套餐"""
        self.ensure_initialized()
        return db.query(Plan).filter(Plan.is_active == True).all()
    
    def get_plan(self, plan_id: int, db: Session) -> Optional[Plan]:
        """获取指定套餐"""
        return db.query(Plan).filter(Plan.id == plan_id, Plan.is_active == True).first()
    
    def create_subscription(self, org_id: int, subscription_data: SubscriptionCreate, db: Session) -> Subscription:
        """创建订阅"""
        plan = self.get_plan(subscription_data.plan_id, db)
        if not plan:
            raise ValueError("套餐不存在")
        
        # 计算金额和周期
        if subscription_data.billing_cycle == BillingCycle.MONTHLY:
            amount = plan.monthly_price
            period_end = datetime.utcnow() + timedelta(days=30)
        else:
            amount = plan.yearly_price
            period_end = datetime.utcnow() + timedelta(days=365)
        
        # 取消现有订阅
        existing_sub = db.query(Subscription).filter(
            Subscription.organization_id == org_id,
            Subscription.status == "active"
        ).first()
        
        if existing_sub:
            existing_sub.status = "cancelled"
            existing_sub.cancelled_at = datetime.utcnow()
        
        # 创建新订阅
        subscription = Subscription(
            organization_id=org_id,
            plan_id=plan.id,
            billing_cycle=subscription_data.billing_cycle,
            amount=amount,
            current_period_end=period_end
        )
        
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        
        # 更新配额
        self._update_quota(org_id, plan, db)
        
        # 生成发票
        if amount > 0:
            self._create_invoice(subscription, db)
        
        return subscription
    
    def _update_quota(self, org_id: int, plan: Plan, db: Session):
        """更新使用配额"""
        quota = db.query(UsageQuota).filter(UsageQuota.organization_id == org_id).first()
        
        if not quota:
            quota = UsageQuota(organization_id=org_id)
            db.add(quota)
        
        quota.api_calls_quota = plan.max_api_calls_monthly
        quota.storage_quota_gb = plan.max_storage_gb
        quota.models_quota = plan.max_models
        quota.reset_date = datetime.utcnow() + timedelta(days=30)
        quota.updated_at = datetime.utcnow()
        
        db.commit()
    
    def _create_invoice(self, subscription: Subscription, db: Session):
        """创建发票"""
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d')}-{subscription.id:06d}"
        
        invoice = Invoice(
            subscription_id=subscription.id,
            organization_id=subscription.organization_id,
            invoice_number=invoice_number,
            subtotal=subscription.amount,
            total_amount=subscription.amount,
            due_date=datetime.utcnow() + timedelta(days=7),
            line_items=json.dumps([{
                "description": f"{subscription.plan.name} - {subscription.billing_cycle}",
                "quantity": 1,
                "unit_price": float(subscription.amount),
                "total": float(subscription.amount)
            }], ensure_ascii=False)
        )
        
        db.add(invoice)
        db.commit()
    
    def get_usage_stats(self, org_id: int, db: Session) -> UsageStats:
        """获取使用统计"""
        quota = db.query(UsageQuota).filter(UsageQuota.organization_id == org_id).first()
        
        if not quota:
            # 创建默认配额
            quota = UsageQuota(organization_id=org_id)
            db.add(quota)
            db.commit()
        
        # 计算使用百分比
        usage_percentage = {
            "api_calls": (quota.api_calls_used / quota.api_calls_quota * 100) if quota.api_calls_quota > 0 else 0,
            "storage": (float(quota.storage_used_gb) / quota.storage_quota_gb * 100) if quota.storage_quota_gb > 0 else 0,
            "models": (quota.models_used / quota.models_quota * 100) if quota.models_quota > 0 else 0
        }
        
        return UsageStats(
            api_calls_used=quota.api_calls_used,
            api_calls_quota=quota.api_calls_quota,
            storage_used_gb=float(quota.storage_used_gb),
            storage_quota_gb=quota.storage_quota_gb,
            models_used=quota.models_used,
            models_quota=quota.models_quota,
            usage_percentage=usage_percentage
        )
    
    def check_quota(self, org_id: int, resource_type: str, amount: int = 1, db: Session = None) -> bool:
        """检查配额是否足够"""
        if not db:
            db = self.get_db()
        
        quota = db.query(UsageQuota).filter(UsageQuota.organization_id == org_id).first()
        if not quota:
            return False
        
        if resource_type == "api_calls":
            return quota.api_calls_used + amount <= quota.api_calls_quota
        elif resource_type == "storage":
            return quota.storage_used_gb + amount <= quota.storage_quota_gb
        elif resource_type == "models":
            return quota.models_used + amount <= quota.models_quota
        
        return False
    
    def consume_quota(self, org_id: int, resource_type: str, amount: int = 1, db: Session = None):
        """消费配额"""
        if not db:
            db = self.get_db()
        
        quota = db.query(UsageQuota).filter(UsageQuota.organization_id == org_id).first()
        if not quota:
            return
        
        if resource_type == "api_calls":
            quota.api_calls_used += amount
        elif resource_type == "storage":
            quota.storage_used_gb += amount
        elif resource_type == "models":
            quota.models_used += amount
        
        quota.updated_at = datetime.utcnow()
        db.commit()

# 全局计费服务实例
billing_service = BillingService()
