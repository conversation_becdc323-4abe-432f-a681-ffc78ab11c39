#!/usr/bin/env python3
"""
修复Cognee存储问题
"""

import os
import sys
import subprocess
from pathlib import Path

def install_chromadb():
    """安装ChromaDB"""
    print("📦 安装ChromaDB...")
    
    try:
        # 尝试导入ChromaDB
        import chromadb
        print("✅ ChromaDB已安装")
        return True
    except ImportError:
        print("❌ ChromaDB未安装，正在安装...")
        
        try:
            # 安装ChromaDB
            subprocess.run([sys.executable, "-m", "pip", "install", "chromadb>=0.4.0"], 
                         check=True, capture_output=True)
            print("✅ ChromaDB安装成功")
            
            # 验证安装
            import chromadb
            print("✅ ChromaDB导入成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ ChromaDB安装失败: {e}")
            return False
        except ImportError as e:
            print(f"❌ ChromaDB导入失败: {e}")
            return False

def install_missing_dependencies():
    """安装缺失的依赖"""
    print("📦 检查并安装缺失的依赖...")
    
    dependencies = [
        "chromadb>=0.4.0",
        "sentence-transformers>=2.2.0", 
        "faiss-cpu>=1.7.0",
        "numpy>=1.21.0",
        "pandas>=1.3.0"
    ]
    
    for dep in dependencies:
        try:
            # 尝试导入包
            package_name = dep.split(">=")[0].replace("-", "_")
            if package_name == "faiss_cpu":
                import faiss
            elif package_name == "sentence_transformers":
                import sentence_transformers
            elif package_name == "chromadb":
                import chromadb
            elif package_name == "numpy":
                import numpy
            elif package_name == "pandas":
                import pandas
            
            print(f"✅ {package_name} 已安装")
            
        except ImportError:
            print(f"❌ {package_name} 未安装，正在安装...")
            
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
                print(f"✅ {package_name} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package_name} 安装失败: {e}")

def setup_api_key():
    """设置API密钥"""
    print("🔑 设置API密钥...")
    
    # 检查现有的API密钥
    existing_keys = {
        'LLM_API_KEY': os.getenv('LLM_API_KEY'),
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY')
    }
    
    has_key = any(existing_keys.values())
    
    if has_key:
        print("✅ 发现现有API密钥")
        for key, value in existing_keys.items():
            if value:
                print(f"  ✅ {key}: {'*' * 10}{value[-4:] if len(value) > 4 else '****'}")
        return True
    
    print("❌ 未找到API密钥")
    print("\n请选择以下方式之一设置API密钥:")
    print("1. 在环境变量中设置 LLM_API_KEY")
    print("2. 在环境变量中设置 OPENAI_API_KEY") 
    print("3. 在环境变量中设置 ANTHROPIC_API_KEY")
    print("\n示例:")
    print("set LLM_API_KEY=sk-your-api-key-here")
    print("或在.env文件中添加:")
    print("LLM_API_KEY=sk-your-api-key-here")
    
    return False

def test_cognee_with_chromadb():
    """测试Cognee与ChromaDB的集成"""
    print("🧪 测试Cognee与ChromaDB集成...")
    
    try:
        # 导入必要的库
        import chromadb
        from cognee_service import cognee_service
        
        print("✅ 库导入成功")
        
        # 创建ChromaDB客户端
        client = chromadb.Client()
        print("✅ ChromaDB客户端创建成功")
        
        # 检查Cognee状态
        status = cognee_service.get_status()
        print(f"📊 Cognee状态: {status}")
        
        # 尝试初始化Cognee（如果有API密钥）
        api_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
        if api_key:
            print("🔧 尝试初始化Cognee...")
            success = cognee_service.initialize_without_test(api_key)
            if success:
                print("✅ Cognee初始化成功")
                return True
            else:
                print("❌ Cognee初始化失败")
        else:
            print("⚠️ 没有API密钥，跳过Cognee初始化")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_test_data():
    """创建测试数据"""
    print("📝 创建测试数据...")
    
    try:
        from cognee_service import cognee_service
        
        # 检查是否已初始化
        if not cognee_service.initialized:
            print("⚠️ Cognee未初始化，跳过测试数据创建")
            return False
        
        # 添加测试文本
        test_text = "这是一个测试文档，用于验证Cognee的ChromaDB存储功能。Cognee是一个AI记忆系统，可以存储和检索知识。"
        
        import asyncio
        
        async def add_test_data():
            success = await cognee_service.add_text(test_text)
            if success:
                print("✅ 测试文本添加成功")
                
                # 执行cognify
                cognify_success = await cognee_service.cognify()
                if cognify_success:
                    print("✅ 知识图谱生成成功")
                    
                    # 测试搜索
                    results = await cognee_service.search("测试")
                    print(f"✅ 搜索测试完成，结果数量: {len(results)}")
                    
                    return True
                else:
                    print("❌ 知识图谱生成失败")
            else:
                print("❌ 测试文本添加失败")
            
            return False
        
        return asyncio.run(add_test_data())
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def check_final_status():
    """检查最终状态"""
    print("🔍 检查最终状态...")
    
    try:
        # 检查数据目录
        data_dir = Path("./cognee_data")
        if data_dir.exists():
            items = list(data_dir.rglob("*"))
            print(f"📁 数据目录: {len(items)} 个文件/目录")
            
            # 显示主要文件
            for item in items[:5]:
                if item.is_file():
                    size = item.stat().st_size
                    print(f"  📄 {item.relative_to(data_dir)} ({size} bytes)")
                else:
                    print(f"  📂 {item.relative_to(data_dir)}/")
        
        # 检查ChromaDB
        import chromadb
        client = chromadb.Client()
        collections = client.list_collections()
        print(f"🗄️ ChromaDB集合数量: {len(collections)}")
        
        for collection in collections:
            count = collection.count()
            print(f"  📚 {collection.name}: {count} 个文档")
        
        # 检查Cognee状态
        from cognee_service import cognee_service
        status = cognee_service.get_status()
        print(f"🧠 Cognee初始化状态: {status.get('initialized', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        return False

def main():
    """主修复流程"""
    print("🔧 Cognee存储修复工具")
    print("=" * 50)
    
    success_steps = 0
    total_steps = 5
    
    # 1. 安装ChromaDB
    if install_chromadb():
        success_steps += 1
    
    # 2. 安装其他依赖
    install_missing_dependencies()
    success_steps += 1
    
    # 3. 设置API密钥
    if setup_api_key():
        success_steps += 1
    
    # 4. 测试集成
    if test_cognee_with_chromadb():
        success_steps += 1
        
        # 5. 创建测试数据
        if create_test_data():
            success_steps += 1
    
    # 检查最终状态
    check_final_status()
    
    print(f"\n📊 修复进度: {success_steps}/{total_steps}")
    
    if success_steps >= 4:
        print("🎉 Cognee存储修复成功!")
        print("\n✅ 现在可以:")
        print("1. 访问 http://localhost:8000/cognee")
        print("2. 添加文档和文本")
        print("3. 生成知识图谱")
        print("4. 进行语义搜索")
    elif success_steps >= 2:
        print("⚠️ 部分修复成功")
        print("请设置API密钥后重新运行此脚本")
    else:
        print("❌ 修复失败")
        print("请检查错误信息并手动解决问题")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断修复")
    except Exception as e:
        print(f"\n❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
