<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP插件市场</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vue@3.2.45/dist/vue.global.prod.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    {% raw %}
    <div id="app" class="container-fluid">
        <div class="row vh-100">
            <!-- 侧边栏 -->
            <div class="col-md-2 col-lg-2 p-0 bg-light sidebar">
                <div class="d-flex flex-column h-100">
                    <div class="p-3 border-bottom">
                        <h4 class="text-center">MCP插件市场</h4>
                    </div>
                    <div class="nav flex-column nav-pills p-3">
                        <button class="nav-link" :class="{ active: activeTab === 'plugins' }" 
                                @click="setActiveTab('plugins')">
                            <i class="bi bi-grid me-2"></i> 插件市场
                        </button>
                        <button class="nav-link" :class="{ active: activeTab === 'installed' }" 
                                @click="setActiveTab('installed')">
                            <i class="bi bi-check-circle me-2"></i> 已安装插件
                        </button>
                        <button class="nav-link" :class="{ active: activeTab === 'running' }" 
                                @click="setActiveTab('running')">
                            <i class="bi bi-play-circle me-2"></i> 运行中插件
                        </button>
                        <button class="nav-link" :class="{ active: activeTab === 'dependencies' }" 
                                @click="setActiveTab('dependencies')">
                            <i class="bi bi-box me-2"></i> 依赖管理
                        </button>
                    </div>
                    <div class="mt-auto p-3 border-top">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" @click="showAddPluginModal">
                                <i class="bi bi-plus-circle me-2"></i> 添加插件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 col-lg-10 p-0 main-content">
                <!-- 插件市场 -->
                <div v-if="activeTab === 'plugins'" class="p-4">
                    <h3>插件市场</h3>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> 从插件市场添加和管理MCP服务
                    </div>
                    
                    <div class="row g-3 mt-3">
                        <div v-for="plugin in availablePlugins" :key="plugin.id" class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ plugin.name || '未命名插件' }}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">{{ plugin.version || '版本未知' }}</h6>
                                    <p class="card-text">{{ plugin.description || '无描述' }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-secondary">{{ plugin.source_type }}</span>
                                        <span class="badge" :class="getStatusClass(plugin.status)">{{ plugin.status }}</span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary" @click="installPlugin(plugin.id)"
                                                :disabled="plugin.status !== 'available' && plugin.status !== 'error'">
                                            <i class="bi bi-download me-1"></i> 安装
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 已安装插件 -->
                <div v-if="activeTab === 'installed'" class="p-4">
                    <h3>已安装插件</h3>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> 管理已安装的MCP插件
                    </div>
                    
                    <div class="row g-3 mt-3">
                        <div v-for="plugin in installedPlugins" :key="plugin.id" class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ plugin.name || '未命名插件' }}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">{{ plugin.version || '版本未知' }}</h6>
                                    <p class="card-text">{{ plugin.description || '无描述' }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-secondary">{{ plugin.source_type }}</span>
                                        <span class="badge" :class="getStatusClass(plugin.status)">{{ plugin.status }}</span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-success flex-grow-1" @click="startPlugin(plugin.id)"
                                                :disabled="plugin.status === 'running' || plugin.status === 'updating'">
                                            <i class="bi bi-play-fill me-1"></i> 启动
                                        </button>
                                        <button class="btn btn-info flex-grow-1" @click="updatePlugin(plugin.id)"
                                                :disabled="plugin.status === 'updating'">
                                            <i class="bi bi-arrow-repeat me-1"></i> 更新
                                        </button>
                                        <button class="btn btn-danger" @click="uninstallPlugin(plugin.id)"
                                                :disabled="plugin.status === 'running' || plugin.status === 'updating'">
                                            <i class="bi bi-trash me-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 运行中插件 -->
                <div v-if="activeTab === 'running'" class="p-4">
                    <h3>运行中插件</h3>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> 查看和管理正在运行的MCP服务
                    </div>
                    
                    <div class="row g-3 mt-3">
                        <div v-for="plugin in runningPlugins" :key="plugin.id" class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ plugin.name || '未命名插件' }}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">{{ plugin.version || '版本未知' }}</h6>
                                    <p class="card-text">{{ plugin.description || '无描述' }}</p>
                                    <div class="mt-3">
                                        <div><strong>端口:</strong> {{ plugin.port }}</div>
                                        <div><strong>PID:</strong> {{ plugin.process_id }}</div>
                                        <div><strong>状态:</strong> <span class="badge bg-success">运行中</span></div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-warning flex-grow-1" @click="restartPlugin(plugin.id)">
                                            <i class="bi bi-arrow-clockwise me-1"></i> 重启
                                        </button>
                                        <button class="btn btn-danger flex-grow-1" @click="stopPlugin(plugin.id)">
                                            <i class="bi bi-stop-fill me-1"></i> 停止
                                        </button>
                                        <button class="btn btn-info" @click="showLogs(plugin.id)">
                                            <i class="bi bi-journal-text me-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 依赖管理 -->
                <div v-if="activeTab === 'dependencies'" class="p-4">
                    <h3>依赖管理</h3>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i> 管理共享的Python依赖环境
                    </div>
                    
                    <div class="d-flex justify-content-end mb-3">
                        <button class="btn btn-primary" @click="showCreateDependencyModal">
                            <i class="bi bi-plus-circle me-1"></i> 创建依赖组
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>Python版本</th>
                                    <th>基础包数量</th>
                                    <th>使用插件</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="group in dependencyGroups" :key="group.id">
                                    <td>{{ group.name }}</td>
                                    <td>{{ group.python_version }}</td>
                                    <td>{{ group.base_packages.length }}</td>
                                    <td>{{ group.plugins.length }}</td>
                                    <td>
                                        <span class="badge" :class="group.active ? 'bg-success' : 'bg-secondary'">
                                            {{ group.active ? '活跃' : '不活跃' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info" @click="viewDependencyDetail(group.id)">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-danger" @click="deleteDependencyGroup(group.id)"
                                                    :disabled="group.plugins.length > 0">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 添加插件模态框 -->
        <div class="modal fade" id="addPluginModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">添加插件</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="repoUrl" class="form-label">仓库URL</label>
                            <input type="text" class="form-control" id="repoUrl" v-model="newPlugin.repo_url" 
                                   placeholder="例如: https://github.com/username/repo">
                            <div class="form-text">支持GitHub、GitLab等Git仓库</div>
                        </div>
                        <div class="mb-3">
                            <label for="branchName" class="form-label">分支名称 (可选)</label>
                            <input type="text" class="form-control" id="branchName" v-model="newPlugin.branch" 
                                   placeholder="例如: main">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="addPlugin">添加</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 创建依赖组模态框 -->
        <div class="modal fade" id="createDependencyModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">创建依赖组</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="dependencyName" class="form-label">名称</label>
                            <input type="text" class="form-control" id="dependencyName" v-model="newDependency.name" 
                                   placeholder="例如: common_env">
                        </div>
                        <div class="mb-3">
                            <label for="pythonVersion" class="form-label">Python版本</label>
                            <select class="form-select" id="pythonVersion" v-model="newDependency.python_version">
                                <option value="3.6">Python 3.6</option>
                                <option value="3.7">Python 3.7</option>
                                <option value="3.8">Python 3.8</option>
                                <option value="3.9">Python 3.9</option>
                                <option value="3.10">Python 3.10</option>
                                <option value="3.11">Python 3.11</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="basePackages" class="form-label">基础依赖包 (每行一个)</label>
                            <textarea class="form-control" id="basePackages" v-model="basePackagesText" 
                                      rows="5" placeholder="例如:&#10;flask==2.0.1&#10;requests>=2.25.1&#10;numpy"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" @click="createDependencyGroup">创建</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 查看日志模态框 -->
        <div class="modal fade" id="logsModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-scrollable">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">插件日志</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="logType" id="logTypeStdout" value="stdout" v-model="currentLogType" autocomplete="off" checked>
                                <label class="btn btn-outline-primary" for="logTypeStdout">标准输出</label>
                                <input type="radio" class="btn-check" name="logType" id="logTypeStderr" value="stderr" v-model="currentLogType" autocomplete="off">
                                <label class="btn btn-outline-primary" for="logTypeStderr">标准错误</label>
                            </div>
                        </div>
                        <pre class="bg-dark text-light p-3 rounded" style="height: 400px; overflow: auto;">{{ logContent }}</pre>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" @click="refreshLogs">刷新</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Toast提示 -->
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
            <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">提示</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body" v-html="toastMessage"></div>
            </div>
        </div>
    </div>
    {% endraw %}
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/mcp_market_main.js"></script>
</body>
</html>
