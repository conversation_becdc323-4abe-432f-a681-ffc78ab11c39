#!/usr/bin/env python3
"""
简单的数据库修复脚本
解决 "no such table: plans" 错误
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装必要的依赖"""
    print("📦 安装必要的依赖...")
    
    dependencies = [
        "sqlalchemy>=2.0.9",
        "bcrypt>=4.0.1", 
        "PyJWT>=2.8.0",
        "email-validator>=2.0.0",
        "aiosqlite>=0.18.0"
    ]
    
    for dep in dependencies:
        try:
            print(f"安装 {dep}...")
            subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                         check=True, capture_output=True)
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError:
            print(f"⚠️ {dep} 安装失败，可能已安装")

def create_database():
    """创建数据库"""
    print("🗄️ 创建数据库...")
    
    # 删除旧的数据库文件
    db_file = Path("business.db")
    if db_file.exists():
        print("🗑️ 删除旧数据库文件...")
        db_file.unlink()
    
    # 创建数据库初始化脚本
    init_script = '''
import os
import sys
from datetime import datetime, timedelta
import json

# 设置数据库URL
os.environ["DATABASE_URL"] = "sqlite:///./business.db"

try:
    from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey, Numeric
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker, relationship
    import bcrypt
    
    # 创建数据库引擎
    engine = create_engine("sqlite:///./business.db", connect_args={"check_same_thread": False})
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    Base = declarative_base()
    
    # 定义数据模型
    class User(Base):
        __tablename__ = "users"
        
        id = Column(Integer, primary_key=True, index=True)
        email = Column(String, unique=True, index=True, nullable=False)
        username = Column(String, unique=True, index=True, nullable=False)
        hashed_password = Column(String, nullable=False)
        full_name = Column(String)
        is_active = Column(Boolean, default=True)
        is_verified = Column(Boolean, default=False)
        created_at = Column(DateTime, default=datetime.utcnow)
        last_login = Column(DateTime)

    class Organization(Base):
        __tablename__ = "organizations"
        
        id = Column(Integer, primary_key=True, index=True)
        name = Column(String, nullable=False)
        slug = Column(String, unique=True, index=True, nullable=False)
        description = Column(Text)
        plan = Column(String, default="free")
        is_active = Column(Boolean, default=True)
        created_at = Column(DateTime, default=datetime.utcnow)
        max_users = Column(Integer, default=5)
        max_api_calls = Column(Integer, default=1000)
        max_storage_gb = Column(Integer, default=1)

    class UserOrganization(Base):
        __tablename__ = "user_organizations"
        
        id = Column(Integer, primary_key=True, index=True)
        user_id = Column(Integer, ForeignKey("users.id"))
        organization_id = Column(Integer, ForeignKey("organizations.id"))
        role = Column(String, default="member")
        joined_at = Column(DateTime, default=datetime.utcnow)

    class Plan(Base):
        __tablename__ = "plans"
        
        id = Column(Integer, primary_key=True, index=True)
        name = Column(String, unique=True, nullable=False)
        type = Column(String, nullable=False)
        description = Column(Text)
        monthly_price = Column(Numeric(10, 2), default=0)
        yearly_price = Column(Numeric(10, 2), default=0)
        max_users = Column(Integer, default=1)
        max_api_calls_monthly = Column(Integer, default=1000)
        max_storage_gb = Column(Integer, default=1)
        max_models = Column(Integer, default=1)
        max_mcp_servers = Column(Integer, default=1)
        features = Column(Text)
        is_active = Column(Boolean, default=True)
        created_at = Column(DateTime, default=datetime.utcnow)

    class Subscription(Base):
        __tablename__ = "subscriptions"
        
        id = Column(Integer, primary_key=True, index=True)
        organization_id = Column(Integer, ForeignKey("organizations.id"))
        plan_id = Column(Integer, ForeignKey("plans.id"))
        billing_cycle = Column(String, default="monthly")
        status = Column(String, default="active")
        started_at = Column(DateTime, default=datetime.utcnow)
        current_period_start = Column(DateTime, default=datetime.utcnow)
        current_period_end = Column(DateTime)
        cancelled_at = Column(DateTime)
        amount = Column(Numeric(10, 2))
        currency = Column(String, default="CNY")

    class UsageQuota(Base):
        __tablename__ = "usage_quotas"
        
        id = Column(Integer, primary_key=True, index=True)
        organization_id = Column(Integer, ForeignKey("organizations.id"))
        api_calls_quota = Column(Integer, default=1000)
        storage_quota_gb = Column(Integer, default=1)
        models_quota = Column(Integer, default=1)
        api_calls_used = Column(Integer, default=0)
        storage_used_gb = Column(Numeric(10, 3), default=0)
        models_used = Column(Integer, default=0)
        reset_date = Column(DateTime)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow)

    class UsageRecord(Base):
        __tablename__ = "usage_records"
        
        id = Column(Integer, primary_key=True, index=True)
        user_id = Column(Integer, ForeignKey("users.id"))
        organization_id = Column(Integer, ForeignKey("organizations.id"))
        endpoint = Column(String)
        method = Column(String)
        tokens_used = Column(Integer, default=0)
        cost = Column(Integer, default=0)
        timestamp = Column(DateTime, default=datetime.utcnow)

    # 创建所有表
    print("📋 创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建成功")
    
    # 创建会话
    db = SessionLocal()
    
    try:
        # 创建默认套餐
        print("📦 创建默认套餐...")
        
        default_plans = [
            {
                "name": "免费版",
                "type": "free",
                "description": "适合个人用户和小型项目",
                "monthly_price": 0,
                "yearly_price": 0,
                "max_users": 1,
                "max_api_calls_monthly": 1000,
                "max_storage_gb": 1,
                "max_models": 1,
                "max_mcp_servers": 1,
                "features": ["基础聊天", "1个AI模型", "基础记忆", "社区支持"]
            },
            {
                "name": "个人版",
                "type": "personal",
                "description": "适合个人开发者和小团队",
                "monthly_price": 99,
                "yearly_price": 999,
                "max_users": 3,
                "max_api_calls_monthly": 10000,
                "max_storage_gb": 10,
                "max_models": 5,
                "max_mcp_servers": 5,
                "features": ["多模型支持", "高级记忆", "MCP插件", "API访问", "邮件支持"]
            },
            {
                "name": "团队版",
                "type": "team",
                "description": "适合中小团队和企业",
                "monthly_price": 299,
                "yearly_price": 2999,
                "max_users": 10,
                "max_api_calls_monthly": 50000,
                "max_storage_gb": 50,
                "max_models": 20,
                "max_mcp_servers": 20,
                "features": ["团队协作", "权限管理", "数据分析", "优先支持", "定制集成"]
            },
            {
                "name": "企业版",
                "type": "enterprise",
                "description": "适合大型企业和组织",
                "monthly_price": 999,
                "yearly_price": 9999,
                "max_users": 100,
                "max_api_calls_monthly": 500000,
                "max_storage_gb": 500,
                "max_models": 100,
                "max_mcp_servers": 100,
                "features": ["私有部署", "SSO集成", "审计日志", "24/7支持", "专属客服", "定制开发"]
            }
        ]
        
        for plan_data in default_plans:
            plan = Plan(
                name=plan_data["name"],
                type=plan_data["type"],
                description=plan_data["description"],
                monthly_price=plan_data["monthly_price"],
                yearly_price=plan_data["yearly_price"],
                max_users=plan_data["max_users"],
                max_api_calls_monthly=plan_data["max_api_calls_monthly"],
                max_storage_gb=plan_data["max_storage_gb"],
                max_models=plan_data["max_models"],
                max_mcp_servers=plan_data["max_mcp_servers"],
                features=json.dumps(plan_data["features"], ensure_ascii=False)
            )
            db.add(plan)
        
        # 创建管理员用户
        print("👤 创建管理员用户...")
        
        hashed_password = bcrypt.hashpw("admin123456".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            hashed_password=hashed_password,
            full_name="系统管理员",
            is_verified=True
        )
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        # 创建管理员组织
        admin_org = Organization(
            name="管理员组织",
            slug="admin-org",
            description="系统管理员组织",
            plan="enterprise"
        )
        db.add(admin_org)
        db.commit()
        db.refresh(admin_org)
        
        # 关联用户和组织
        user_org = UserOrganization(
            user_id=admin_user.id,
            organization_id=admin_org.id,
            role="owner"
        )
        db.add(user_org)
        
        db.commit()
        
        # 验证数据
        plan_count = db.query(Plan).count()
        user_count = db.query(User).count()
        org_count = db.query(Organization).count()
        
        print(f"✅ 创建完成:")
        print(f"  📦 套餐: {plan_count} 个")
        print(f"  👥 用户: {user_count} 个")
        print(f"  🏢 组织: {org_count} 个")
        
        print("\\n🔑 管理员账户:")
        print("  邮箱: <EMAIL>")
        print("  密码: admin123456")
        
    finally:
        db.close()
        
    print("🎉 数据库初始化完成!")
    
except Exception as e:
    print(f"❌ 数据库创建失败: {e}")
    sys.exit(1)
'''
    
    # 写入临时脚本文件
    script_file = Path("temp_init_db.py")
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    try:
        # 运行初始化脚本
        result = subprocess.run([sys.executable, str(script_file)], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据库创建成功")
            print(result.stdout)
        else:
            print(f"❌ 数据库创建失败: {result.stderr}")
            return False
    finally:
        # 清理临时文件
        if script_file.exists():
            script_file.unlink()
    
    return True

def main():
    """主修复流程"""
    print("🔧 IntelliHub Pro - 简单数据库修复")
    print("=" * 50)
    
    # 1. 安装依赖
    install_dependencies()
    
    # 2. 创建数据库
    if not create_database():
        print("❌ 数据库创建失败")
        sys.exit(1)
    
    print("\n🎉 修复完成!")
    print("\n📋 下一步:")
    print("1. 启动应用: python app.py")
    print("2. 访问主页: http://localhost:8000")
    print("3. 访问企业仪表板: http://localhost:8000/business")
    print("\n🔑 管理员账户:")
    print("  邮箱: <EMAIL>")
    print("  密码: admin123456")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出修复")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        sys.exit(1)
