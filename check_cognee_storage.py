#!/usr/bin/env python3
"""
检查Cognee数据存储情况
"""

import os
import sys
from pathlib import Path
import json

def check_cognee_data_directory():
    """检查cognee数据目录"""
    print("🔍 检查Cognee数据存储...")
    print("=" * 50)
    
    # 检查默认数据目录
    data_dirs = [
        "./cognee_data",
        "./data",
        os.path.expanduser("~/.cognee"),
        os.path.expanduser("~/cognee_data"),
    ]
    
    for data_dir in data_dirs:
        data_path = Path(data_dir)
        print(f"\n📁 检查目录: {data_path.absolute()}")
        
        if data_path.exists():
            print(f"✅ 目录存在")
            
            # 列出目录内容
            try:
                items = list(data_path.iterdir())
                if items:
                    print(f"📋 目录内容 ({len(items)} 项):")
                    for item in items:
                        if item.is_dir():
                            sub_items = list(item.iterdir()) if item.exists() else []
                            print(f"  📂 {item.name}/ ({len(sub_items)} 项)")
                            # 显示子目录内容
                            for sub_item in sub_items[:5]:  # 只显示前5项
                                print(f"    - {sub_item.name}")
                            if len(sub_items) > 5:
                                print(f"    ... 还有 {len(sub_items) - 5} 项")
                        else:
                            size = item.stat().st_size if item.exists() else 0
                            print(f"  📄 {item.name} ({size} bytes)")
                else:
                    print("📭 目录为空")
            except Exception as e:
                print(f"❌ 无法读取目录内容: {e}")
        else:
            print(f"❌ 目录不存在")

def check_cognee_config():
    """检查Cognee配置"""
    print("\n🔧 检查Cognee配置...")
    print("=" * 30)
    
    try:
        from cognee_service import cognee_service
        status = cognee_service.get_status()
        
        print("📊 Cognee服务状态:")
        print(f"  可用: {status.get('available', False)}")
        print(f"  已初始化: {status.get('initialized', False)}")
        print(f"  数据目录: {status.get('data_dir', 'N/A')}")
        
        config = status.get('config', {})
        if config:
            print("⚙️ 配置信息:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        
        return status
        
    except Exception as e:
        print(f"❌ 无法获取Cognee状态: {e}")
        return None

def check_chromadb():
    """检查ChromaDB数据"""
    print("\n🗄️ 检查ChromaDB数据...")
    print("=" * 30)
    
    try:
        import chromadb
        print("✅ ChromaDB已安装")
        
        # 检查可能的ChromaDB数据目录
        possible_dirs = [
            "./cognee_data/chroma",
            "./cognee_data/chromadb", 
            "./chroma_db",
            "./chromadb",
            "./.chroma",
            os.path.expanduser("~/.chroma"),
        ]
        
        for chroma_dir in possible_dirs:
            chroma_path = Path(chroma_dir)
            print(f"\n📁 检查ChromaDB目录: {chroma_path.absolute()}")
            
            if chroma_path.exists():
                print(f"✅ 目录存在")
                
                # 列出ChromaDB文件
                try:
                    items = list(chroma_path.rglob("*"))
                    if items:
                        print(f"📋 ChromaDB文件 ({len(items)} 项):")
                        for item in items[:10]:  # 只显示前10项
                            if item.is_file():
                                size = item.stat().st_size
                                print(f"  📄 {item.relative_to(chroma_path)} ({size} bytes)")
                            else:
                                print(f"  📂 {item.relative_to(chroma_path)}/")
                        if len(items) > 10:
                            print(f"  ... 还有 {len(items) - 10} 项")
                    else:
                        print("📭 目录为空")
                except Exception as e:
                    print(f"❌ 无法读取ChromaDB目录: {e}")
            else:
                print(f"❌ 目录不存在")
        
        # 尝试连接到ChromaDB
        try:
            print("\n🔗 尝试连接ChromaDB...")
            client = chromadb.Client()
            collections = client.list_collections()
            print(f"✅ ChromaDB连接成功，集合数量: {len(collections)}")
            
            for collection in collections:
                print(f"  📚 集合: {collection.name}")
                try:
                    count = collection.count()
                    print(f"    文档数量: {count}")
                except Exception as e:
                    print(f"    无法获取文档数量: {e}")
                    
        except Exception as e:
            print(f"❌ ChromaDB连接失败: {e}")
            
    except ImportError:
        print("❌ ChromaDB未安装")

def check_cognee_internals():
    """检查Cognee内部状态"""
    print("\n🧠 检查Cognee内部状态...")
    print("=" * 30)
    
    try:
        import cognee
        print("✅ Cognee已导入")
        
        # 检查Cognee的配置
        if hasattr(cognee, 'config'):
            print("⚙️ Cognee配置可用")
        else:
            print("⚠️ Cognee配置不可用")
        
        # 检查环境变量
        api_keys = [
            'LLM_API_KEY',
            'OPENAI_API_KEY', 
            'ANTHROPIC_API_KEY'
        ]
        
        print("🔑 API密钥状态:")
        for key in api_keys:
            value = os.getenv(key)
            if value:
                print(f"  ✅ {key}: {'*' * 10}{value[-4:] if len(value) > 4 else '****'}")
            else:
                print(f"  ❌ {key}: 未设置")
                
    except ImportError as e:
        print(f"❌ Cognee导入失败: {e}")

def search_for_data_files():
    """搜索可能的数据文件"""
    print("\n🔍 搜索数据文件...")
    print("=" * 30)
    
    # 搜索可能的数据文件扩展名
    extensions = ['.db', '.sqlite', '.chroma', '.parquet', '.json', '.pkl']
    
    current_dir = Path('.')
    
    for ext in extensions:
        print(f"\n🔎 搜索 *{ext} 文件:")
        files = list(current_dir.rglob(f"*{ext}"))
        
        if files:
            for file in files[:5]:  # 只显示前5个
                size = file.stat().st_size
                print(f"  📄 {file} ({size} bytes)")
            if len(files) > 5:
                print(f"  ... 还有 {len(files) - 5} 个文件")
        else:
            print(f"  ❌ 未找到 {ext} 文件")

def main():
    """主检查流程"""
    print("🔍 Cognee数据存储检查工具")
    print("=" * 50)
    
    # 1. 检查数据目录
    check_cognee_data_directory()
    
    # 2. 检查Cognee配置
    status = check_cognee_config()
    
    # 3. 检查ChromaDB
    check_chromadb()
    
    # 4. 检查Cognee内部状态
    check_cognee_internals()
    
    # 5. 搜索数据文件
    search_for_data_files()
    
    print("\n📋 总结:")
    print("=" * 20)
    
    if status and status.get('initialized'):
        print("✅ Cognee服务已初始化")
        data_dir = status.get('data_dir')
        if data_dir and Path(data_dir).exists():
            print(f"✅ 数据目录存在: {data_dir}")
        else:
            print("⚠️ 数据目录可能不存在或为空")
    else:
        print("❌ Cognee服务未初始化或不可用")
    
    print("\n💡 建议:")
    print("1. 如果数据目录为空，尝试添加一些文本并执行cognify")
    print("2. 检查API密钥是否正确设置")
    print("3. 查看应用日志了解详细错误信息")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断检查")
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
