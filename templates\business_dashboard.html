<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IntelliHub Pro - 企业级AI工作台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .metric-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .metric-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .metric-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .sidebar-item.active {
            background: rgba(255,255,255,0.1);
            border-left: 4px solid #fff;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold">
                            <i class="fas fa-brain mr-2"></i>
                            IntelliHub Pro
                        </h1>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 通知 -->
                    <button class="relative p-2 rounded-full hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
                    </button>
                    
                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 p-2 rounded-lg hover:bg-white hover:bg-opacity-10">
                            <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Admin&background=fff&color=667eea" alt="用户头像">
                            <span class="hidden md:block">管理员</span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex h-screen bg-gray-50">
        <!-- 侧边栏 -->
        <div class="w-64 gradient-bg text-white">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="#dashboard" class="sidebar-item active flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        <span>仪表板</span>
                    </a>
                    <a href="#analytics" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-chart-line mr-3"></i>
                        <span>数据分析</span>
                    </a>
                    <a href="#users" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-users mr-3"></i>
                        <span>用户管理</span>
                    </a>
                    <a href="#organizations" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-building mr-3"></i>
                        <span>组织管理</span>
                    </a>
                    <a href="#billing" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-credit-card mr-3"></i>
                        <span>计费管理</span>
                    </a>
                    <a href="#api" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-code mr-3"></i>
                        <span>API管理</span>
                    </a>
                    <a href="#settings" class="sidebar-item flex items-center px-4 py-3 rounded-lg hover:bg-white hover:bg-opacity-10">
                        <i class="fas fa-cog mr-3"></i>
                        <span>系统设置</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 overflow-auto">
            <div class="p-8">
                <!-- 页面标题 -->
                <div class="mb-8">
                    <h2 class="text-3xl font-bold text-gray-900">企业仪表板</h2>
                    <p class="text-gray-600 mt-2">实时监控您的AI工作台运营状况</p>
                </div>

                <!-- 关键指标卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="metric-card blue text-white p-6 rounded-xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">总用户数</p>
                                <p class="text-3xl font-bold" id="totalUsers">-</p>
                                <p class="text-blue-100 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +12% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                                <i class="fas fa-users text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card green text-white p-6 rounded-xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100 text-sm">月度收入</p>
                                <p class="text-3xl font-bold" id="monthlyRevenue">-</p>
                                <p class="text-green-100 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +8% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                                <i class="fas fa-dollar-sign text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card purple text-white p-6 rounded-xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100 text-sm">API调用</p>
                                <p class="text-3xl font-bold" id="apiCalls">-</p>
                                <p class="text-purple-100 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +25% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                                <i class="fas fa-code text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card text-white p-6 rounded-xl card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-pink-100 text-sm">活跃组织</p>
                                <p class="text-3xl font-bold" id="activeOrgs">-</p>
                                <p class="text-pink-100 text-sm mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    +5% 本月
                                </p>
                            </div>
                            <div class="bg-white bg-opacity-20 p-3 rounded-lg">
                                <i class="fas fa-building text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- 用户增长图表 -->
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">用户增长趋势</h3>
                            <select class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                                <option>最近30天</option>
                                <option>最近90天</option>
                                <option>最近一年</option>
                            </select>
                        </div>
                        <canvas id="userGrowthChart" height="300"></canvas>
                    </div>

                    <!-- 收入分析图表 -->
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">收入分析</h3>
                            <select class="text-sm border border-gray-300 rounded-lg px-3 py-1">
                                <option>按月</option>
                                <option>按季度</option>
                                <option>按年</option>
                            </select>
                        </div>
                        <canvas id="revenueChart" height="300"></canvas>
                    </div>
                </div>

                <!-- API使用情况和热门功能 -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                    <!-- API使用情况 -->
                    <div class="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm card-hover">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">API使用情况</h3>
                        <canvas id="apiUsageChart" height="200"></canvas>
                    </div>

                    <!-- 热门功能 -->
                    <div class="bg-white p-6 rounded-xl shadow-sm card-hover">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">热门功能</h3>
                        <div id="topFeatures" class="space-y-4">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 组织洞察 -->
                <div class="bg-white rounded-xl shadow-sm card-hover">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">组织洞察</h3>
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                查看全部
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">组织</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">套餐</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">用户数</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">API调用</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">健康评分</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="organizationTable">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let userGrowthChart, revenueChart, apiUsageChart;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            initializeCharts();
            
            // 每30秒刷新一次数据
            setInterval(loadDashboardData, 30000);
        });

        // 加载仪表板数据
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/business/dashboard/stats');
                const data = await response.json();
                
                // 更新关键指标
                document.getElementById('totalUsers').textContent = data.total_users.toLocaleString();
                document.getElementById('monthlyRevenue').textContent = `¥${data.revenue_month.toLocaleString()}`;
                document.getElementById('apiCalls').textContent = data.total_api_calls_month.toLocaleString();
                document.getElementById('activeOrgs').textContent = data.total_organizations.toLocaleString();
                
                // 更新热门功能
                updateTopFeatures(data.top_features);
                
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            }
        }

        // 更新热门功能
        function updateTopFeatures(features) {
            const container = document.getElementById('topFeatures');
            container.innerHTML = '';
            
            features.forEach((feature, index) => {
                const item = document.createElement('div');
                item.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
                item.innerHTML = `
                    <div class="flex items-center">
                        <span class="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center mr-3">
                            ${index + 1}
                        </span>
                        <span class="font-medium text-gray-900">${feature.name}</span>
                    </div>
                    <span class="text-sm text-gray-500">${feature.usage_count}</span>
                `;
                container.appendChild(item);
            });
        }

        // 初始化图表
        function initializeCharts() {
            // 用户增长图表
            const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
            userGrowthChart = new Chart(userGrowthCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '用户数量',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 收入分析图表
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            revenueChart = new Chart(revenueCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '收入',
                        data: [],
                        backgroundColor: 'rgba(67, 233, 123, 0.8)',
                        borderColor: '#43e97b',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // API使用情况图表
            const apiUsageCtx = document.getElementById('apiUsageChart').getContext('2d');
            apiUsageChart = new Chart(apiUsageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['聊天API', 'Cognee API', 'MCP API', '其他'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 加载图表数据
            loadChartData();
        }

        // 加载图表数据
        async function loadChartData() {
            try {
                // 加载用户分析数据
                const userResponse = await fetch('/api/business/analytics/users?days=30');
                const userData = await userResponse.json();
                
                // 更新用户增长图表
                userGrowthChart.data.labels = userData.user_growth.map(item => item.date);
                userGrowthChart.data.datasets[0].data = userData.user_growth.map(item => item.total_users);
                userGrowthChart.update();

                // 加载收入分析数据
                const revenueResponse = await fetch('/api/business/analytics/revenue?months=6');
                const revenueData = await revenueResponse.json();
                
                // 更新收入图表
                revenueChart.data.labels = revenueData.revenue_trend.map(item => item.month);
                revenueChart.data.datasets[0].data = revenueData.revenue_trend.map(item => item.revenue);
                revenueChart.update();

            } catch (error) {
                console.error('加载图表数据失败:', error);
            }
        }

        // 侧边栏导航
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活跃状态
                document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                
                // 添加活跃状态到当前项
                this.classList.add('active');
                
                // 这里可以添加页面切换逻辑
                const page = this.getAttribute('href').substring(1);
                console.log('切换到页面:', page);
            });
        });
    </script>
</body>
</html>
