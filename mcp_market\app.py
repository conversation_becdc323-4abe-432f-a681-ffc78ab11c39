"""
MCP插件市场主应用入口
"""
import os
import sys
import logging
import argparse
from pathlib import Path
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse

# 使用try-except块导入可能有问题的模块
try:
    from mcp_market.core.plugin_manager import PluginManager
    from mcp_market.core.repository import RepositoryManager
    from mcp_market.core.dependency import DependencyManager
    from mcp_market.services.process_manager import ProcessManager
    
    # 尝试导入API路由
    try:
        from mcp_market.api.routes import router as api_router, get_plugin_manager, dependencies_router
        routes_imported = True
    except ImportError as e:
        print(f"警告: 无法导入API路由: {e}")
        # 创建默认路由对象
        from fastapi import APIRouter
        api_router = APIRouter()
        dependencies_router = APIRouter()
        
        def get_plugin_manager():
            raise NotImplementedError("未能正确导入get_plugin_manager函数")
            
        routes_imported = False
        
    all_modules_imported = True
except ImportError as e:
    print(f"错误: 无法导入核心模块: {e}")
    all_modules_imported = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('mcp_market.log')
    ]
)

logger = logging.getLogger(__name__)


class MCPMarket:
    """MCP插件市场应用"""
    
    def __init__(self, base_dir: str, host: str = "0.0.0.0", port: int = 8000, plugin_port_base: int = 9000):
        """初始化MCP插件市场
        
        Args:
            base_dir: 基础目录
            host: 监听主机
            port: 监听端口
            plugin_port_base: 插件服务端口基数
        """
        self.base_dir = Path(base_dir)
        self.host = host
        self.port = port
        
        # 确保目录存在
        self._ensure_directories()
        
        # 检查模块是否已导入
        if not all_modules_imported:
            logger.error("核心模块导入失败，应用程序将以有限功能运行")
            self.app = FastAPI(
                title="MCP插件市场 (有限功能)",
                description="由于模块导入错误，仅以有限功能运行",
                version="0.1.0"
            )
            return
            
        # 创建组件
        self.repo_manager = RepositoryManager(
            plugins_dir=str(self.base_dir / "plugins")
        )
        
        self.dep_manager = DependencyManager(
            env_dir=str(self.base_dir / "envs"),
            cache_dir=str(self.base_dir / "cache")
        )
        
        self.process_manager = ProcessManager(
            logs_dir=str(self.base_dir / "logs"),
            base_port=plugin_port_base,
            on_process_exit=self._handle_process_exit
        )
        
        self.plugin_manager = PluginManager(
            data_dir=str(self.base_dir),
            repo_manager=self.repo_manager,
            dep_manager=self.dep_manager,
            process_manager=self.process_manager,
            storage_type="sqlite"
        )
        
        # 创建FastAPI应用
        self.app = FastAPI(
            title="MCP插件市场",
            description="轻量级、资源共享的MCP服务插件生态系统",
            version="0.1.0"
        )
        
        # 初始化应用
        self._setup_app()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        dirs = [
            "plugins",
            "envs",
            "logs",
            "cache",
            "data",
            "storage"
        ]
        
        for dir_name in dirs:
            os.makedirs(self.base_dir / dir_name, exist_ok=True)
    
    def _setup_app(self):
        """设置FastAPI应用"""
        # 如果模块导入失败，则创建一个简单的应用
        if not all_modules_imported:
            @self.app.get("/")
            async def error_index():
                return {"error": "模块导入失败，应用程序无法完全初始化"}
            return
            
        # 配置CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 设置静态文件
        static_dir = Path(__file__).parent / "static"
        if static_dir.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
        
        # 设置模板
        self.templates = Jinja2Templates(directory=str(Path(__file__).parent / "templates"))
        
        # 注册API路由
        if routes_imported:
            self.app.include_router(api_router)
            self.app.include_router(dependencies_router)
            
            # 注册依赖项
            self._setup_dependencies()
        else:
            logger.warning("API路由导入失败，API功能将不可用")
        
        # 注册其他路由
        self._setup_routes()
        
        # 注册启动和关闭事件
        self._setup_events()
    
    def _setup_dependencies(self):
        """设置依赖项"""
        # 替换获取插件管理器的函数
        def get_plugin_manager_impl():
            return self.plugin_manager
        
        # 在FastAPI应用上设置依赖覆盖，而不是在路由上
        self.app.dependency_overrides[get_plugin_manager] = get_plugin_manager_impl
    
    def _setup_routes(self):
        """设置其他路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def index(request: Request):
            """主页"""
            # Redirect to the static index.html file instead of using Jinja2 template
            try:
                return HTMLResponse(content=open(str(Path(__file__).parent / "static/index.html"), "r", encoding="utf-8").read())
            except Exception as e:
                logger.exception(f"读取首页模板时出错: {e}")
                return HTMLResponse(content=f"<html><body><h1>MCP插件市场</h1><p>加载首页时出错: {e}</p></body></html>")
    
    def _setup_events(self):
        """设置启动和关闭事件"""
        
        @self.app.on_event("startup")
        async def startup_event():
            """应用启动事件"""
            logger.info("MCP插件市场启动")
            
            # 如果模块正确导入，则启动已安装的插件
            if all_modules_imported:
                # 启动已安装的插件
                self._start_installed_plugins()
        
        @self.app.on_event("shutdown")
        async def shutdown_event():
            """应用关闭事件"""
            logger.info("MCP插件市场关闭")
            
            # 如果模块正确导入，则关闭进程管理器
            if all_modules_imported:
                # 关闭进程管理器
                self.process_manager.shutdown()
    
    def _start_installed_plugins(self):
        """启动已安装的插件"""
        if not all_modules_imported:
            logger.warning("由于模块导入错误，无法启动插件")
            return
            
        autostart_plugins = []
        
        for plugin_id, plugin in self.plugin_manager.plugins.items():
            if plugin.status.value == "installed" and plugin.metadata.get("autostart", False):
                autostart_plugins.append(plugin_id)
        
        logger.info(f"自动启动 {len(autostart_plugins)} 个插件")
        
        for plugin_id in autostart_plugins:
            try:
                success, error, port = self.plugin_manager.start_plugin(plugin_id)
                if success:
                    logger.info(f"插件 {plugin_id} 自动启动成功，端口: {port}")
                else:
                    logger.error(f"插件 {plugin_id} 自动启动失败: {error}")
            except Exception as e:
                logger.exception(f"自动启动插件 {plugin_id} 时发生异常: {e}")
    
    def _handle_process_exit(self, plugin_id: str, exit_code: int):
        """处理进程退出事件"""
        if not all_modules_imported:
            return
            
        logger.info(f"插件 {plugin_id} 进程退出，退出代码: {exit_code}")
        
        # 更新插件状态
        self.plugin_manager._handle_process_exit(plugin_id, exit_code)
        
        # 如果配置了自动重启，则尝试重启
        plugin = self.plugin_manager.get_plugin(plugin_id)
        if plugin and plugin.metadata.get("auto_restart", False):
            logger.info(f"尝试自动重启插件 {plugin_id}")
            try:
                success, error, port = self.plugin_manager.restart_plugin(plugin_id)
                if success:
                    logger.info(f"插件 {plugin_id} 自动重启成功，端口: {port}")
                else:
                    logger.error(f"插件 {plugin_id} 自动重启失败: {error}")
            except Exception as e:
                logger.exception(f"自动重启插件 {plugin_id} 时发生异常: {e}")
    
    def run(self):
        """运行应用"""
        logger.info(f"启动Web服务器: {self.host}:{self.port}")
        # 打印到标准输出以便调试
        print(f"启动Web服务器: {self.host}:{self.port}")
        
        try:
            uvicorn.run(self.app, host=self.host, port=self.port)
        except Exception as e:
            logger.exception(f"启动Web服务器时出错: {e}")
            print(f"启动Web服务器时出错: {e}")


def main():
    """主入口"""
    parser = argparse.ArgumentParser(description="MCP插件市场")
    parser.add_argument("--host", default="0.0.0.0", help="监听主机")
    parser.add_argument("--port", type=int, default=8000, help="监听端口")
    parser.add_argument("--data-dir", default="./mcp_market_data", help="数据目录")
    
    args = parser.parse_args()
    
    # 创建并运行应用
    try:
        print("创建MCP Market应用...")
        app = MCPMarket(
            base_dir=args.data_dir,
            host=args.host,
            port=args.port
        )
        print("运行MCP Market应用...")
        app.run()
    except Exception as e:
        logger.exception(f"创建或运行应用时出错: {e}")
        print(f"创建或运行应用时出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    print("MCP Market主程序开始执行")
    main() 