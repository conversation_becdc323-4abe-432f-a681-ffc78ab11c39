from openai import OpenAI
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import requests
import yaml
from fastapi import FastAPI, Request, HTTPException, File, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, StreamingResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel
import asyncio

# 导入MCP市场的相关组件
try:
    from mcp_market.core.plugin_manager import PluginManager
    from mcp_market.core.repository import RepositoryManager
    from mcp_market.core.dependency import DependencyManager
    from mcp_market.services.process_manager import ProcessManager
    MCP_MARKET_AVAILABLE = True
except ImportError:
    print("Warning: MCP Market components not available")
    MCP_MARKET_AVAILABLE = False

# Import our standard MCP client
try:
    from standard_mcp_client import create_standard_client, MCP_STANDARD_AVAILABLE
except ImportError:
    print("Warning: Standard MCP client not available")
    MCP_STANDARD_AVAILABLE = False

# Import our direct MCP client
try:
    from direct_mcp_client import create_direct_client
    MCP_DIRECT_AVAILABLE = True
except ImportError:
    print("Warning: Direct MCP client not available")
    MCP_DIRECT_AVAILABLE = False

# 导入我们的SSE客户端工具
try:
    from sse_client import SSEMCPClient
    SSE_CLIENT_AVAILABLE = True
except ImportError:
    print("Warning: SSE client not available")
    SSE_CLIENT_AVAILABLE = False

    # Create a stub implementation of SSEMCPClient
    class SSEMCPClient:
        def __init__(self, *args, **kwargs):
            pass
        def health_check(self):
            return {"status": "error", "message": "SSE client not available"}

# 导入Cognee服务
try:
    from cognee_service import cognee_service, COGNEE_AVAILABLE
    print("Cognee service imported successfully")
except ImportError as e:
    print(f"Warning: Cognee service not available: {e}")
    COGNEE_AVAILABLE = False

    # 创建一个存根实现
    class DummyCogneeService:
        def __init__(self):
            pass
        async def initialize(self, *args, **kwargs):
            return False
        async def add_text(self, *args, **kwargs):
            return False
        async def search(self, *args, **kwargs):
            return []
        def get_status(self):
            return {"available": False, "initialized": False}

    cognee_service = DummyCogneeService()

# 导入商业化系统
try:
    from auth_system import auth_service, User, Organization, APIKey, UserCreate, UserLogin, Token
    from billing_system import billing_service, Plan, Subscription, SubscriptionCreate, UsageStats
    from dashboard_system import dashboard_service, DashboardStats, UserAnalytics, UsageAnalytics, RevenueAnalytics
    BUSINESS_AVAILABLE = True
    print("Business systems imported successfully")
except ImportError as e:
    print(f"Warning: Business systems not available: {e}")
    BUSINESS_AVAILABLE = False
except Exception as e:
    print(f"Warning: Business systems initialization error: {e}")
    print("This is normal on first run - database tables will be created automatically")
    BUSINESS_AVAILABLE = False

# Add stub implementations for missing classes 
try:
    from pydantic import TypeAdapter
except ImportError:
    print("Warning: TypeAdapter not available in pydantic. Creating stub implementation.")
    # Create stub TypeAdapter
    class TypeAdapter:
        def __init__(self, *args, **kwargs):
            pass
        def validate_python(self, *args, **kwargs):
            return args[0] if args else None

# Add stub for DocumentBlockParam
try:
    from anthropic.types import DocumentBlockParam
except ImportError:
    print("Warning: DocumentBlockParam not available in anthropic.types. Creating stub implementation.")
    # Create stub DocumentBlockParam
    class DocumentBlockParam:
        def __init__(self, *args, **kwargs):
            self.text = kwargs.get('text', '')
            self.type = kwargs.get('type', 'text')

# Create empty classes that can be used when MCP is not available
class DummyClient:
    """A dummy client that mimics the MCP client API"""
    def __init__(self, server_name="dummy"):
        self.server_name = server_name

    async def get_tools(self):
        """Return an empty tools list"""
        logger.warning(f"使用DummyClient.get_tools(): MCP功能不可用")
        return []
        
    async def call_tool(self, tool_name, params=None):
        """Return a dummy response for tool calls"""
        logger.warning(f"使用DummyClient.call_tool({tool_name}): MCP功能不可用")
        return {
            "error": "MCP功能不可用",
            "detail": "可能原因: 1) MCP服务器未运行; 2) MCP客户端未安装; 3) 版本不兼容"
        }
        
    async def __aenter__(self):
        """Support being used as an async context manager"""
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Handle exit from async context manager"""
        pass
        
class DummyServerRegistry:
    def __init__(self):
        self.servers = {}
    
    def get_server(self, name):
        return {"name": name, "type": "dummy", "description": "Dummy server for when MCP is not available"}
        
dummy_server_registry = DummyServerRegistry()

try:
    # These imports are no longer needed as we are not using mcp-agent
    # from mcp_agent.agents.agent import Agent
    # from mcp_agent.workflows.llm.augmented_llm_openai import OpenAIAugmentedLLM
    # from mcp_agent.workflows.llm.augmented_llm_anthropic import AnthropicAugmentedLLM
    # from mcp_agent.workflows.orchestrator.orchestrator import Orchestrator
    # from mcp_agent.context import get_current_context
    MCP_AVAILABLE = False # Set to False as mcp-agent is removed
except ImportError as e:
    import traceback
    # This part is not strictly necessary anymore but can be kept for logging if other issues arise
    print(f"Warning: mcp-agent not available: {e}")
    traceback.print_exc()
    print("You can still use the interface, but MCP functionality will be limited.")
    
    # Define stub classes/functions for when MCP is not available
    # Agent = DummyAgent # No longer needed
    # OpenAIAugmentedLLM = DummyLLM # No longer needed
    # AnthropicAugmentedLLM = DummyLLM # No longer needed
    # Orchestrator = DummyLLM # No longer needed
    
    # def get_current_context(*args, **kwargs): # No longer needed
    #     return {}
    
    MCP_AVAILABLE = False

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="AI Chat with MCP", description="AI对话网站，支持模型选择和MCP服务器配置")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置模板和静态文件
templates = Jinja2Templates(directory="templates")
app.mount("/static", StaticFiles(directory="static"), name="static")

# 数据模型
class ChatMessage(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    message: str
    model: str
    api_key: Optional[str] = None
    mcp_servers: List[str] = []
    stream: bool = False  # 新增流式输出选项
    session_id: str = "default"  # 新增会话ID，用于维护上下文

class ModelConfig(BaseModel):
    name: str
    provider: str
    api_key: str
    api_url: Optional[str] = None  # 新增API URL配置
    model_name: Optional[str] = None  # 新增具体模型名称配置

class MCPServerConfig(BaseModel):
    name: str
    description: Optional[str] = None
    
    # 连接类型
    connection_type: str = "stdio"  # "stdio", "sse", "streamableHttp"
    
    # stdio 配置
    command: Optional[str] = None
    args: Optional[List[str]] = None
    env: Optional[Dict[str, str]] = None
    cwd: Optional[str] = None
    
    # HTTP 配置 (用于 sse 和 streamableHttp)
    url: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    
    # 高级配置
    timeout: Optional[int] = 30
    retry_count: Optional[int] = 3

class MCPHealthCheckRequest(BaseModel):
    server_names: List[str] = []

# 预定义的提供商配置
PROVIDER_CONFIGS = {
    "openai": {
        "name": "OpenAI",
        "default_url": "https://api.openai.com/v1",
        "default_models": ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"]
    },
    "anthropic": {
        "name": "Anthropic",
        "default_url": "https://api.anthropic.com",
        "default_models": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]
    },
    "deepseek": {
        "name": "DeepSeek",
        "default_url": "https://api.deepseek.com/v1",
        "default_models": ["deepseek-chat", "deepseek-coder"]
    },
    "qwen": {
        "name": "Qwen (通义千问)",
        "default_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "default_models": ["qwen-turbo", "qwen-plus", "qwen-max"]
    },
    "zhipu": {
        "name": "智谱AI",
        "default_url": "https://open.bigmodel.cn/api/paas/v4",
        "default_models": ["glm-4", "glm-3-turbo"]
    },
    "moonshot": {
        "name": "Moonshot (月之暗面)",
        "default_url": "https://api.moonshot.cn/v1",
        "default_models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"]
    }
}

# 预置MCP服务器配置示例
MCP_SERVER_EXAMPLES = {
    "fetch": {
        "name": "fetch",
        "description": "Fetch content at URLs from the world wide web",
        "connection_type": "stdio",
        "command": "uvx",
        "args": ["mcp-server-fetch"],
        "timeout": 30,
        "retry_count": 3
    },
    "filesystem": {
        "name": "filesystem",
        "description": "File system operations with security restrictions",
        "connection_type": "stdio",
        "command": "uvx",
        "args": ["mcp-server-filesystem", "--allowed-dirs", "."],
        "timeout": 30,
        "retry_count": 3
    },
    "sqlite": {
        "name": "sqlite",
        "description": "SQLite database operations",
        "connection_type": "stdio",
        "command": "uvx",
        "args": ["mcp-server-sqlite", "--db-path", "./data.db"],
        "timeout": 30,
        "retry_count": 3
    },
    "memory": {
        "name": "memory",
        "description": "Memory and note-taking capabilities",
        "connection_type": "stdio",
        "command": "uvx",
        "args": ["mcp-server-memory"],
        "timeout": 30,
        "retry_count": 3
    },
    "http-api": {
        "name": "example-api",
        "description": "Example HTTP API service",
        "connection_type": "sse",
        "url": "https://api.example.com/mcp",
        "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer your-token-here"
        },
        "timeout": 30,
        "retry_count": 3
    }
}

# 存储配置
configs = {
    "models": {},
    "mcp_servers": {},
    "chat_history": [],
    "chat_sessions": {}  # 新增: 用于存储会话上下文，格式：{session_id: [消息列表]}
}

# MCP服务器状态缓存
mcp_server_status = {}

# 会话管理类，用于处理对话上下文
class SessionManager:
    @staticmethod
    def get_session(session_id: str = "default") -> List[Dict]:
        """获取会话历史，如果不存在则创建新会话"""
        if session_id not in configs["chat_sessions"]:
            configs["chat_sessions"][session_id] = []
        return configs["chat_sessions"][session_id]
        
    @staticmethod
    def add_message(session_id: str, role: str, content: str):
        """添加消息到会话历史"""
        session = SessionManager.get_session(session_id)
        session.append({"role": role, "content": content})
        return session
        
    @staticmethod
    def clear_session(session_id: str):
        """清空会话历史"""
        if session_id in configs["chat_sessions"]:
            configs["chat_sessions"][session_id] = []
        return True

# 加载配置文件
def load_config():
    config_path = Path("config.yaml")
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded = yaml.safe_load(f) or {}
                return {
                    "models": loaded.get("models", {}),
                    "mcp_servers": loaded.get("mcp_servers", {}),
                    "chat_history": loaded.get("chat_history", []),
                    "chat_sessions": loaded.get("chat_sessions", {})
                }
        except Exception as e:
            logger.error(f"Error loading config: {e}")
    return {"models": {}, "mcp_servers": {}, "chat_history": [], "chat_sessions": {}}

def save_config():
    try:
        with open("config.yaml", 'w', encoding='utf-8') as f:
            yaml.dump(configs, f, allow_unicode=True, default_flow_style=False)
    except Exception as e:
        logger.error(f"Error saving config: {e}")

async def check_mcp_server_health(server_name, config):
    # Add implementation for health check
    health_result = {
        "status": "ok",
        "message": "Server is available",
        "timestamp": datetime.now().isoformat()
    }
    
    try:
        # You can implement actual health check logic here
        # For example, try to connect to the server based on config
        
        # For now, just return a simple success result
        return health_result
    except Exception as e:
        # Log the error
        logger.error(f"Health check failed for {server_name}: {str(e)}")
        # Return error status
        return {
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 初始化配置
configs.update(load_config())

# 初始化MCP市场组件
mcp_plugin_manager = None
if MCP_MARKET_AVAILABLE:
    try:
        base_dir = Path("./mcp_market_data")
        os.makedirs(base_dir, exist_ok=True)
        
        repo_manager = RepositoryManager(
            plugins_dir=str(base_dir / "plugins")
        )
        
        dep_manager = DependencyManager(
            env_dir=str(base_dir / "envs"),
            cache_dir=str(base_dir / "cache")
        )
        
        process_manager = ProcessManager(
            logs_dir=str(base_dir / "logs"),
            base_port=9000
        )
        
        mcp_plugin_manager = PluginManager(
            data_dir=str(base_dir),
            repo_manager=repo_manager,
            dep_manager=dep_manager,
            process_manager=process_manager,
            storage_type="sqlite"
        )
        
        print("MCP Market components initialized successfully")
    except Exception as e:
        print(f"Error initializing MCP Market components: {e}")
        MCP_MARKET_AVAILABLE = False

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("Starting AI Chat with MCP...")

    # 显示功能可用性
    if MCP_STANDARD_AVAILABLE:
        logger.info("使用官方MCP客户端标准实现")
    elif MCP_DIRECT_AVAILABLE:
        logger.info("使用直接MCP客户端实现")
    else:
        logger.warning("MCP功能不可用 - 仅使用基本聊天功能")

    # 初始化Cognee服务
    if COGNEE_AVAILABLE:
        logger.info("初始化Cognee服务...")
        try:
            # 尝试从配置中获取API密钥
            api_key = None
            provider = "openai"

            # 从已配置的模型中获取第一个可用的API密钥
            for model_name, model_config in configs["models"].items():
                if model_config.get("api_key"):
                    api_key = model_config["api_key"]
                    provider = model_config.get("provider", "openai")
                    break

            if api_key:
                success = await cognee_service.initialize(api_key, provider)
                if success:
                    logger.info("Cognee服务初始化成功")
                else:
                    logger.warning("Cognee服务初始化失败")
            else:
                logger.warning("未找到API密钥，Cognee服务将在首次使用时初始化")
        except Exception as e:
            logger.error(f"Cognee服务初始化错误: {e}")
    else:
        logger.warning("Cognee不可用")

    # 启动时检查所有MCP服务器健康状态
    if configs["mcp_servers"]:
        logger.info("Checking MCP servers health...")
        global mcp_server_status

    # 启动已安装的MCP插件
    if MCP_MARKET_AVAILABLE and mcp_plugin_manager:
        try:
            # 获取所有已安装的插件
            installed_plugins = mcp_plugin_manager.get_installed_plugins()
            for plugin in installed_plugins:
                # 检查插件是否配置为自动启动
                if plugin.get("auto_start", False):
                    try:
                        print(f"Auto-starting MCP plugin: {plugin['name']}")
                        mcp_plugin_manager.start_plugin(plugin['id'])
                    except Exception as e:
                        print(f"Error auto-starting plugin {plugin['name']}: {e}")
        except Exception as e:
            print(f"Error starting installed MCP plugins: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    # 停止所有运行中的MCP插件
    if MCP_MARKET_AVAILABLE and mcp_plugin_manager:
        try:
            running_plugins = mcp_plugin_manager.get_running_plugins()
            for plugin in running_plugins:
                try:
                    print(f"Stopping MCP plugin: {plugin['name']}")
                    mcp_plugin_manager.stop_plugin(plugin['id'])
                except Exception as e:
                    print(f"Error stopping plugin {plugin['name']}: {e}")
        except Exception as e:
            print(f"Error stopping running MCP plugins: {e}")

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """主页面"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "models": configs["models"],
        "mcp_servers": configs["mcp_servers"],
        "provider_configs": PROVIDER_CONFIGS,
        "mcp_examples": MCP_SERVER_EXAMPLES
    })

@app.get("/integrated", response_class=HTMLResponse)
async def integrated_page(request: Request):
    """集成AI聊天MCP插件"""
    return templates.TemplateResponse("mcp_market_index.html", {"request": request})

@app.get("/knowledge-base", response_class=HTMLResponse)
async def knowledge_base_page(request: Request):
    return templates.TemplateResponse("knowledge_base.html", {"request": request})

@app.get("/cognee", response_class=HTMLResponse)
async def cognee_page(request: Request):
    """Cognee AI记忆系统界面"""
    return templates.TemplateResponse("cognee_interface.html", {"request": request})

@app.get("/business", response_class=HTMLResponse)
async def business_dashboard(request: Request):
    """企业级仪表板"""
    return templates.TemplateResponse("business_dashboard.html", {"request": request})

# ==================== Cognee API 端点 ====================

@app.get("/api/cognee/status")
async def get_cognee_status():
    """获取Cognee服务状态"""
    return cognee_service.get_status()

@app.post("/api/cognee/initialize")
async def initialize_cognee(request: dict):
    """初始化Cognee服务"""
    api_key = request.get("api_key")
    provider = request.get("provider", "openai")
    skip_test = request.get("skip_test", False)

    if not api_key:
        raise HTTPException(status_code=400, detail="需要提供API密钥")

    try:
        if skip_test:
            # 使用简化初始化
            success = cognee_service.initialize_without_test(api_key, provider)
        else:
            # 使用完整初始化（包含测试）
            success = await cognee_service.initialize(api_key, provider)

        if success:
            return {
                "message": "Cognee服务初始化成功",
                "status": "success",
                "test_skipped": skip_test
            }
        else:
            raise HTTPException(status_code=500, detail="Cognee服务初始化失败")
    except Exception as e:
        logger.error(f"初始化Cognee时出错: {e}")
        # 如果完整初始化失败，尝试简化初始化
        if not skip_test:
            logger.info("尝试简化初始化...")
            try:
                success = cognee_service.initialize_without_test(api_key, provider)
                if success:
                    return {
                        "message": "Cognee服务初始化成功（简化模式）",
                        "status": "success",
                        "test_skipped": True,
                        "warning": "功能测试失败，但基本配置已完成"
                    }
            except Exception as fallback_error:
                logger.error(f"简化初始化也失败: {fallback_error}")

        raise HTTPException(status_code=500, detail=f"Cognee服务初始化失败: {str(e)}")

@app.post("/api/cognee/add-text")
async def add_text_to_cognee(request: dict):
    """添加文本到Cognee知识库"""
    text = request.get("text")
    metadata = request.get("metadata", {})

    if not text:
        raise HTTPException(status_code=400, detail="需要提供文本内容")

    success = await cognee_service.add_text(text, metadata)
    if success:
        return {"message": "文本已成功添加到Cognee", "status": "success"}
    else:
        raise HTTPException(status_code=500, detail="添加文本失败")

@app.post("/api/cognee/add-file")
async def add_file_to_cognee(file: UploadFile = File(...)):
    """上传文件到Cognee知识库"""
    try:
        # 保存上传的文件到临时位置
        temp_file_path = f"./temp_{file.filename}"
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # 添加文件到cognee
        success = await cognee_service.add_file(temp_file_path)

        # 清理临时文件
        os.remove(temp_file_path)

        if success:
            return {"message": f"文件 {file.filename} 已成功添加到Cognee", "status": "success"}
        else:
            raise HTTPException(status_code=500, detail="添加文件失败")

    except Exception as e:
        # 确保清理临时文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise HTTPException(status_code=500, detail=f"处理文件时出错: {str(e)}")

@app.post("/api/cognee/cognify")
async def cognify():
    """执行cognify操作，生成知识图谱"""
    success = await cognee_service.cognify()
    if success:
        return {"message": "知识图谱生成成功", "status": "success"}
    else:
        raise HTTPException(status_code=500, detail="知识图谱生成失败")

@app.post("/api/cognee/search")
async def search_cognee(request: dict):
    """在Cognee知识库中搜索"""
    query = request.get("query")
    limit = request.get("limit", 5)

    if not query:
        raise HTTPException(status_code=400, detail="需要提供搜索查询")

    results = await cognee_service.search(query, limit)
    return {
        "query": query,
        "results": results,
        "count": len(results)
    }

@app.post("/api/cognee/reset")
async def reset_cognee():
    """重置Cognee数据"""
    success = await cognee_service.reset()
    if success:
        return {"message": "Cognee数据已重置", "status": "success"}
    else:
        raise HTTPException(status_code=500, detail="重置Cognee数据失败")

@app.get("/api/providers")
async def get_providers():
    """获取支持的提供商列表"""
    return PROVIDER_CONFIGS

@app.get("/api/mcp-examples")
async def get_mcp_examples():
    """获取MCP服务器配置示例"""
    return MCP_SERVER_EXAMPLES

@app.get("/api/models")
async def get_models():
    """获取已配置的模型"""
    return configs["models"]

@app.post("/api/models")
async def add_model(model: ModelConfig):
    """添加新模型配置"""
    configs["models"][model.name] = {
        "provider": model.provider,
        "api_key": model.api_key,
        "api_url": model.api_url or PROVIDER_CONFIGS.get(model.provider, {}).get("default_url"),
        "model_name": model.model_name or model.name
    }
    save_config()
    return {"message": "模型配置已保存"}

@app.delete("/api/models/{model_name}")
async def delete_model(model_name: str):
    """删除模型配置"""
    if model_name in configs["models"]:
        del configs["models"][model_name]
        save_config()
        return {"message": "模型配置已删除"}
    raise HTTPException(status_code=404, detail="模型不存在")

@app.get("/api/mcp-servers")
async def get_mcp_servers():
    """获取已配置的MCP服务器"""
    return configs["mcp_servers"]

@app.get("/api/mcp-servers/health")
async def get_mcp_servers_health():
    """获取所有MCP服务器的健康状态"""
    global mcp_server_status
    return mcp_server_status

@app.post("/api/mcp-servers/health-check")
async def check_mcp_servers_health(request: MCPHealthCheckRequest):
    """手动检查指定MCP服务器的健康状态"""
    results = {}
    
    servers_to_check = request.server_names if request.server_names else list(configs["mcp_servers"].keys())
    
    for server_name in servers_to_check:
        if server_name in configs["mcp_servers"]:
            config = configs["mcp_servers"][server_name]
            results[server_name] = await check_mcp_server_health(server_name, config)
    
    # 更新全局状态缓存
    global mcp_server_status
    mcp_server_status.update(results)
    
    return results

@app.post("/api/mcp-servers")
async def add_mcp_server(server: MCPServerConfig):
    """添加新MCP服务器配置"""
    
    # 构建配置对象
    config_data = {
        "connection_type": server.connection_type,
        "description": server.description or "",
        "timeout": server.timeout or 30,
        "retry_count": server.retry_count or 3
    }
    
    if server.connection_type == "stdio":
        if not server.command:
            raise HTTPException(status_code=400, detail="stdio连接类型需要指定命令")
        config_data.update({
            "command": server.command,
            "args": server.args or [],
            "env": server.env or {},
            "cwd": server.cwd
        })
    elif server.connection_type in ["sse", "streamableHttp"]:
        if not server.url:
            raise HTTPException(status_code=400, detail=f"{server.connection_type}连接类型需要指定URL")
        config_data.update({
            "url": server.url,
            "headers": server.headers or {}
        })
    else:
        raise HTTPException(status_code=400, detail="不支持的连接类型")
    
    configs["mcp_servers"][server.name] = config_data
    save_config()
    
    # 添加服务器后立即检查健康状态
    health_status = await check_mcp_server_health(server.name, config_data)
    global mcp_server_status
    mcp_server_status[server.name] = health_status
    
    return {
        "message": "MCP服务器配置已保存",
        "health_status": health_status
    }

@app.delete("/api/mcp-servers/{server_name}")
async def delete_mcp_server(server_name: str):
    """删除MCP服务器配置"""
    if server_name in configs["mcp_servers"]:
        del configs["mcp_servers"][server_name]
        
        # 同时删除健康状态
        global mcp_server_status
        if server_name in mcp_server_status:
            del mcp_server_status[server_name]
            
        save_config()
        return {"message": "MCP服务器配置已删除"}
    raise HTTPException(status_code=404, detail="MCP服务器不存在")

@app.post("/api/chat")
async def chat(request: ChatRequest):
    """处理聊天请求"""
    try:
        # 获取模型配置
        if request.model not in configs["models"]:
            raise HTTPException(status_code=400, detail="模型未配置")
        
        model_config = configs["models"][request.model]
        
        # 使用提供的API密钥或配置中的密钥
        api_key = request.api_key or model_config["api_key"]
        
        if not api_key:
            raise HTTPException(status_code=400, detail="未提供API密钥")
        
        # 检查选中的MCP服务器健康状态
        if request.mcp_servers:
            unhealthy_servers = []
            for server_name in request.mcp_servers:
                if server_name in mcp_server_status:
                    status = mcp_server_status[server_name].get("status", "unknown")
                    if status == "error":
                        unhealthy_servers.append(server_name)
            
            if unhealthy_servers:
                logger.warning(f"Using unhealthy MCP servers: {unhealthy_servers}")
        
        # 如果请求流式输出，返回流式响应
        if request.stream:
            # 自定义响应头，确保SSE流能正确工作
            response_headers = {
                "Content-Type": "text/event-stream",
                "Cache-Control": "no-cache, no-transform",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "X-Accel-Buffering": "no"
            }
            
            logger.info(f"POST端点建立流式连接 - 模型: {request.model}")
            
            return StreamingResponse(
                stream_chat_and_save_history(request.message, model_config, api_key, request.mcp_servers, request.model, request.session_id),
                media_type="text/event-stream",
                headers=response_headers
            )
        
        # 否则使用标准响应
        if not request.mcp_servers:
            # 如果没有选择MCP服务器，使用简单的聊天模式
            response = await simple_chat(request.message, model_config, api_key, request.session_id)
        else:
            # 使用MCP功能
            if MCP_DIRECT_AVAILABLE:
                # 优先使用我们的直接MCP实现
                response = await direct_mcp_chat(request.message, model_config, api_key, request.mcp_servers, request.session_id)
            elif MCP_AVAILABLE:
                # 如果直接实现不可用但mcp-agent可用，使用mcp-agent
                response = await mcp_chat(request.message, model_config, api_key, request.mcp_servers, request.session_id)
            else:
                # 如果两者都不可用，使用简单聊天
                logger.warning("MCP功能不可用，使用简单聊天模式")
                response = await simple_chat(request.message, model_config, api_key, request.session_id)
        
        # 保存聊天历史
        if "chat_history" not in configs:
            configs["chat_history"] = []

        configs["chat_history"].append({
            "user": request.message,
            "assistant": response,
            "model": request.model,
            "mcp_servers": request.mcp_servers,
            "timestamp": datetime.now().isoformat()
        })

        # 如果cognee可用，将对话保存到cognee记忆中
        if COGNEE_AVAILABLE and cognee_service.initialized:
            try:
                await cognee_service.add_conversation(
                    request.message,
                    response,
                    request.session_id
                )
                logger.info("对话已保存到Cognee记忆中")
            except Exception as e:
                logger.warning(f"保存对话到Cognee失败: {e}")

        return {"response": response}
        
    except Exception as e:
        logger.error(f"Chat error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"聊天处理错误: {str(e)}")

@app.get("/api/chat")
async def stream_chat(
    message: str,
    model: str,
    stream: bool = True,
    api_key: str = "",
    mcp_servers: str = ""
):
    """使用GET方法处理流式聊天请求"""
    try:
        # 解析MCP服务器参数 - 逗号分隔的字符串
        mcp_server_list = mcp_servers.split(",") if mcp_servers else []
        mcp_server_list = [s for s in mcp_server_list if s]  # 移除空字符串
        
        # 获取模型配置
        if model not in configs["models"]:
            raise HTTPException(status_code=400, detail="模型未配置")
        
        model_config = configs["models"][model]
        
        # 使用提供的API密钥或配置中的密钥
        api_key = api_key or model_config["api_key"]
        
        if not api_key:
            raise HTTPException(status_code=400, detail="未提供API密钥")
        
        # 检查选中的MCP服务器健康状态
        if mcp_server_list and MCP_AVAILABLE:
            unhealthy_servers = []
            for server_name in mcp_server_list:
                if server_name in mcp_server_status:
                    status = mcp_server_status[server_name].get("status", "unknown")
                    if status == "error":
                        unhealthy_servers.append(server_name)
            
            if unhealthy_servers:
                logger.warning(f"Using unhealthy MCP servers: {unhealthy_servers}")
        
        # 自定义响应头，确保SSE流能正确工作
        response_headers = {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache, no-transform",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "X-Accel-Buffering": "no"  # 对Nginx很重要
        }
        
        logger.info(f"建立流式连接 - 模型: {model}, 使用EventSource")
        
        # 返回流式响应，使用更新的参数以确保不缓冲
        return StreamingResponse(
            stream_chat_and_save_history(message, model_config, api_key, mcp_server_list, model, "default"),
            media_type="text/event-stream",
            headers=response_headers,
        )
        
    except Exception as e:
        logger.error(f"Stream chat error: {str(e)}")
        # 返回错误消息作为SSE事件
        async def error_stream():
            yield f"data: {json.dumps({'content': f'错误: {str(e)}', 'done': True})}\n\n"
        return StreamingResponse(
            error_stream(), 
            media_type="text/event-stream",
            headers={
                "Content-Type": "text/event-stream", 
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Cache-Control": "no-cache"
            }
        )

async def stream_chat_and_save_history(message: str, model_config: dict, api_key: str, mcp_servers: List[str], model_name: str, session_id: str = "default"):
    """流式聊天并在结束时保存历史记录"""
    full_response = ""
    
    # 保存用户消息到会话
    SessionManager.add_message(session_id, "user", message)
    
    # 添加一个保持连接的初始消息并立即刷新缓冲区
    yield f"data: {json.dumps({'content': '', 'done': False})}\n\n"
    await asyncio.sleep(0.01)  # 小延迟确保刷新
    
    # 使用不同的处理方式
    if not mcp_servers:
        # 如果没有选择MCP服务器，使用简单的聊天流
        try:
            logger.info(f"使用流式输出处理消息，模型: {model_name}")
            async for chunk in stream_chat(message, model_config, api_key, session_id):
                if chunk["done"]:
                    # 流结束时保存聊天历史
                    if "chat_history" not in configs:
                        configs["chat_history"] = []
                    
                    configs["chat_history"].append({
                        "user": message,
                        "assistant": full_response,
                        "model": model_name,
                        "mcp_servers": mcp_servers,
                        "timestamp": datetime.now().isoformat(),
                        "streamed": True
                    })
                    
                    # 发送最终的SSE事件
                    yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"
                else:
                    # 追加到完整响应
                    full_response += chunk["content"]
                    
                    # 发送SSE事件并立即刷新
                    event_data = f"data: {json.dumps(chunk)}\n\n"
                    yield event_data
                    await asyncio.sleep(0.01)  # 小延迟确保刷新
        except Exception as e:
            logger.error(f"Stream error: {str(e)}")
            yield f"data: {json.dumps({'content': f'流式输出错误: {str(e)}', 'done': True})}\n\n"
    else:
        # MCP不支持流式输出，使用标准响应后模拟流式输出
        try:
            logger.info(f"使用MCP处理消息（模拟流式输出），模型: {model_name}")
            
            if MCP_DIRECT_AVAILABLE:
                # 优先使用我们的直接MCP实现
                response = await direct_mcp_chat(message, model_config, api_key, mcp_servers, session_id)
            elif MCP_AVAILABLE:
                # 如果直接实现不可用但mcp-agent可用，使用mcp-agent
                response = await mcp_chat(message, model_config, api_key, mcp_servers, session_id)
            else:
                # 如果两者都不可用，使用简单聊天
                logger.warning("MCP功能不可用，使用简单聊天模式")
                response = await simple_chat(message, model_config, api_key, session_id)
                
            full_response = response
            
            # 模拟流式输出 - 使用合理的块大小
            chunk_size = 2  # 进一步减小块大小，让流更明显
            
            # 强制分段
            for i in range(0, len(response), chunk_size):
                chunk = response[i:i+chunk_size]
                event_data = f"data: {json.dumps({'content': chunk, 'done': False})}\n\n"
                yield event_data
                # 关键：每个块后添加短暂延迟，强制刷新缓冲区
                await asyncio.sleep(0.1)  # 增加延迟使流更明显
            
            # 保存聊天历史
            if "chat_history" not in configs:
                configs["chat_history"] = []
                
            configs["chat_history"].append({
                "user": message,
                "assistant": full_response,
                "model": model_name,
                "mcp_servers": mcp_servers,
                "timestamp": datetime.now().isoformat(),
                "streamed": True
            })
            
            # 发送最终SSE事件并刷新
            yield f"data: {json.dumps({'content': '', 'done': True})}\n\n"
            await asyncio.sleep(0.01)  # 确保最后一个事件被刷新
        except Exception as e:
            logger.error(f"MCP stream error: {str(e)}")
            yield f"data: {json.dumps({'content': f'MCP流式输出错误: {str(e)}', 'done': True})}\n\n"

async def simple_chat(message: str, model_config: dict, api_key: str, session_id: str = "default") -> str:
    """简单的聊天实现（当mcp-agent不可用时）"""
    provider = model_config["provider"]
    api_url = model_config.get("api_url")
    model_name = model_config.get("model_name", "gpt-3.5-turbo")
    
    # 获取会话上下文
    chat_history = SessionManager.get_session(session_id)
    
    # 构建消息列表
    messages = []
    for msg in chat_history:
        messages.append({"role": msg["role"], "content": msg["content"]})
    
    # 添加当前用户消息
    messages.append({"role": "user", "content": message})
    
    # 如果是空会话，添加默认系统消息
    if len(chat_history) == 0:
        messages.insert(0, {"role": "system", "content": "你是一个有用的AI助手。"})
    
    if provider == "anthropic":
        try:
            import anthropic
            # Anthropic 有自己的API格式
            if api_url and api_url != PROVIDER_CONFIGS["anthropic"]["default_url"]:
                client = anthropic.Anthropic(api_key=api_key, base_url=api_url)
            else:
                client = anthropic.Anthropic(api_key=api_key)
            
            response = client.messages.create(
                model=model_name or "claude-3-haiku-20240307",
                max_tokens=1000,
                messages=messages
            )
            
            # 保存回复到会话
            response_text = response.content[0].text
            SessionManager.add_message(session_id, "assistant", response_text)
            return response_text
            
        except ImportError:
            return "Anthropic库未安装，请安装anthropic包"
        except Exception as e:
            return f"Anthropic API错误: {str(e)}"
    
    else:
        # 对于OpenAI兼容的API（包括OpenAI、DeepSeek、Qwen等）
        try:
            import openai
            
            # 使用自定义URL或默认URL
            if api_url:
                openai.api_base = api_url
            
            # 设置API密钥
            openai.api_key = api_key
            
            response = openai.ChatCompletion.create(
                model=model_name,
                messages=messages,
                max_tokens=1000
            )
            
            # 保存回复到会话
            response_text = response.choices[0].message.content
            SessionManager.add_message(session_id, "assistant", response_text)
            return response_text
            
        except ImportError:
            return "OpenAI库未安装，请安装openai包"
        except Exception as e:
            return f"API错误: {str(e)}"

async def stream_chat(message: str, model_config: dict, api_key: str, session_id: str = "default"):
    """支持流式(streaming)输出的聊天实现"""
    provider = model_config["provider"]
    api_url = model_config.get("api_url")
    model_name = model_config.get("model_name", "gpt-3.5-turbo")
    
    # 获取会话上下文
    chat_history = SessionManager.get_session(session_id)
    
    # 构建消息列表
    messages = []
    for msg in chat_history:
        messages.append({"role": msg["role"], "content": msg["content"]})
    
    # 添加当前用户消息并保存到会话
    messages.append({"role": "user", "content": message})
    SessionManager.add_message(session_id, "user", message)
    
    # 如果是空会话，添加默认系统消息
    if len(chat_history) == 0:
        messages.insert(0, {"role": "system", "content": "你是一个有用的AI助手。"})
    
    # 用于收集完整响应
    full_response = ""
    
    if provider == "anthropic":
        try:
            import anthropic
            
            # Anthropic API格式
            if api_url and api_url != PROVIDER_CONFIGS["anthropic"]["default_url"]:
                client = anthropic.Anthropic(api_key=api_key, base_url=api_url)
            else:
                client = anthropic.Anthropic(api_key=api_key)
            
            # 创建流式响应
            with client.messages.stream(
                model=model_name or "claude-3-haiku-20240307",
                max_tokens=1000,
                messages=messages
            ) as stream:
                for text in stream.text_stream:
                    full_response += text
                    yield {"content": text, "done": False}
                
                # 流结束时保存到会话
                if full_response:
                    SessionManager.add_message(session_id, "assistant", full_response)
                
                # 流结束时发送完成标记
                yield {"content": "", "done": True}
                
        except ImportError:
            yield {"content": "Anthropic库未安装，请安装anthropic包", "done": True}
        except Exception as e:
            yield {"content": f"Anthropic API错误: {str(e)}", "done": True}
    
    else:
        # 对于OpenAI兼容的API
        try:
            import openai
            
            # 使用自定义URL或默认URL
            if api_url:
                client = openai.OpenAI(api_key=api_key, base_url=api_url)
            else:
                client = openai.OpenAI(api_key=api_key)
            
            # 创建流式响应
            stream = client.chat.completions.create(
                model=model_name,
                messages=messages,
                max_tokens=1000,
                stream=True  # 启用流式输出
            )
            
            # 处理流式响应
            for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    if choice.delta.content is not None:
                        content = choice.delta.content
                        if content:
                            full_response += content
                            yield {"content": content, "done": False}
            
            # 保存完整响应到会话
            if full_response:
                SessionManager.add_message(session_id, "assistant", full_response)
            
            # 流结束时发送完成标记
            yield {"content": "", "done": True}
            
        except ImportError:
            yield {"content": "OpenAI库未安装，请安装openai包", "done": True}
        except Exception as e:
            yield {"content": f"API错误: {str(e)}", "done": True}

async def mcp_chat(message: str, model_config: dict, api_key: str, mcp_servers: List[str], session_id: str = "default") -> str:
    """使用标准MCP客户端进行聊天"""
    provider = model_config["provider"]
    model_name = model_config.get("model_name", "gpt-4")
    
    # 获取会话上下文
    chat_history = SessionManager.get_session(session_id)
    
    # 先保存用户消息到会话
    SessionManager.add_message(session_id, "user", message)
    
    try:
        # 设置环境变量
        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
            os.environ["OPENAI_API_KEY"] = api_key
            # 对于兼容OpenAI API的服务，需要设置base_url
            if model_config.get("api_url"):
                os.environ["OPENAI_BASE_URL"] = model_config["api_url"]
        elif provider == "anthropic":
            os.environ["ANTHROPIC_API_KEY"] = api_key
            if model_config.get("api_url"):
                os.environ["ANTHROPIC_BASE_URL"] = model_config["api_url"]
        else:
            raise HTTPException(status_code=400, detail="不支持的模型提供商")
        
        # 获取所有可用MCP服务器的工具列表
        tools_info = []
        for server_name in mcp_servers:
            try:
                if server_name not in configs["mcp_servers"]:
                    continue
                    
                # 检查服务器健康状态
                server_health = mcp_server_status.get(server_name, {})
                if server_health.get("status") == "error":
                    logger.warning(f"服务器 {server_name} 状态异常，跳过工具获取")
                    continue
                
                # 获取工具列表
                client = gen_client(server_name)
                try:
                    tools = await client.get_tools()
                    
                    server_tools = {
                        "server_name": server_name,
                        "tools": tools
                    }
                    tools_info.append(server_tools)
                    
                    logger.info(f"从服务器 {server_name} 获取到 {len(tools)} 个工具")
                except AttributeError as e:
                    logger.error(f"获取服务器 {server_name} 工具失败: {str(e)}")
                    continue
            except Exception as e:
                logger.error(f"获取服务器 {server_name} 工具列表失败: {str(e)}")
        
        # 构建工具描述
        tools_description = ""
        for server_tool in tools_info:
            server_name = server_tool["server_name"]
            tools = server_tool["tools"]
            
            if tools:
                tools_description += f"\n服务器 {server_name} 提供的工具:\n"
                for tool in tools:
                    tool_name = tool.get("name", "未知工具")
                    tool_description = tool.get("description", "无描述")
                    params_info = tool.get("params", {})
                    
                    tools_description += f"- {tool_name}: {tool_description}\n"
                    if params_info:
                        tools_description += f"  参数: {json.dumps(params_info, ensure_ascii=False)}\n"
        
        # 创建系统提示
        system_instruction = f"""你是一个有用的AI助手，具有使用各种工具和服务的能力。

当前可用的MCP服务器工具：{', '.join(mcp_servers) if mcp_servers else '无'}

{tools_description}

请根据用户的问题提供准确和有帮助的回答。根据需要主动使用上述工具，以便更好地回答用户问题。

使用工具时，需要明确：
1. 使用哪个服务器的工具
2. 工具名称
3. 工具所需的参数

当你需要使用工具时，请按照以下格式：
```
我需要使用[工具名称]工具来[目的]。
服务器: [服务器名称]
工具: [工具名称]
参数:
  参数1: 值1
  参数2: 值2
```

执行上述工具调用后，你会收到结果，然后你可以使用这些结果来回答我的问题。
"""

        # 准备聊天历史上下文，只取最近10轮对话
        context_history = chat_history[-10:] if len(chat_history) > 10 else chat_history
        context = "\n\n".join([
            f"{msg['role'].capitalize()}: {msg['content']}" 
            for msg in context_history[:-1]  # 不包括最新的用户消息，因为会单独传入
        ])
        
        # 如果有聊天历史，添加到消息前面
        if context:
            full_message = f"以下是之前的对话历史：\n\n{context}\n\n现在请回答我的新问题：{message}"
        else:
            full_message = message
        
        # 准备完整的聊天历史记录用于LLM输入
        messages = []
        messages.append({"role": "system", "content": system_instruction})
        
        for msg in context_history[:-1]:  # 不包括最新的用户消息
            messages.append({"role": msg["role"], "content": msg["content"]})
            
        messages.append({"role": "user", "content": message})
        
        # 执行对话
        logger.info(f"使用模型 {model_name} 和 MCP 服务器: {', '.join(mcp_servers)}")
        
        response_text = ""
        needs_tool = False
        tool_pattern = "我需要使用"
        
        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
            # OpenAI API调用
            if model_config.get("api_url"):
                client = OpenAI(api_key=api_key, base_url=model_config["api_url"])
            else:
                client = OpenAI(api_key=api_key)
                
            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                max_tokens=2000,
                temperature=0.7
            )
            response_text = response.choices[0].message.content
        # 检查回复是否需要使用工具
        if tool_pattern in response_text:
            # 解析LLM的工具调用请求
            needs_tool = True
            
            # 尝试提取工具信息
            try:
                # 提取格式化的工具调用块
                import re
                tool_block_pattern = r"服务器:\s*([^\n]+)\n工具:\s*([^\n]+)\n参数:([\s\S]+?)(?=```|$)"
                matches = re.search(tool_block_pattern, response_text)
                
                if matches:
                    server_name = matches.group(1).strip()
                    tool_name = matches.group(2).strip()
                    params_text = matches.group(3).strip()
                    
                    # 解析参数
                    params = {}
                    param_pattern = r"(\w+):\s*(.+)(?:\n|$)"
                    param_matches = re.findall(param_pattern, params_text)
                    
                    for param_name, param_value in param_matches:
                        params[param_name.strip()] = param_value.strip()
                    
                    # 检查服务器是否存在
                    if server_name not in configs["mcp_servers"]:
                        response_text += f"\n\n[错误: 服务器 '{server_name}' 不存在]"
                    else:
                        # 调用工具
                        client = gen_client(server_name)
                        tool_result = await client.call_tool(tool_name, params)
                        
                        # 将工具结果添加到会话
                        tool_result_text = f"\n\n工具调用结果：\n```\n{json.dumps(tool_result, ensure_ascii=False, indent=5)}\n```\n\n"
                        
                        # 现在让LLM处理工具结果
                        messages.append({"role": "assistant", "content": response_text})
                        messages.append({"role": "user", "content": f"工具已被调用，这是结果：{tool_result_text}\n请基于这个结果回答我的问题, 你不需要把调用结果展示给我，我只需要你提取后的信息。"})
                        
                        # 再次调用LLM
                        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
                            if model_config.get("api_url"):
                                client = OpenAI(api_key=api_key, base_url=model_config["api_url"])
                            else:
                                client = OpenAI(api_key=api_key)
                                
                            final_response = client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=2000,
                                temperature=0.7
                            )
                            final_text = final_response.choices[0].message.content
                        elif provider == "anthropic":
                            final_response = client.messages.create(
                                model=model_name,
                                max_tokens=2000,
                                messages=messages
                            )
                            final_text = final_response.content[0].text
                        
                        # 组合最终响应
                        response_text = f"{response_text}\n\n{final_text}"
                else:
                    response_text += "\n\n[无法解析工具调用格式]"
            except Exception as e:
                logger.error(f"工具调用失败: {str(e)}")
                response_text += f"\n\n[工具调用失败: {str(e)}]"
        
        # 保存助手回复到会话
        SessionManager.add_message(session_id, "assistant", response_text)
        
        return response_text
        
    except Exception as e:
        logger.error(f"MCP聊天错误: {str(e)}")
        # 如果MCP调用失败，回退到简单聊天
        fallback_response = await simple_chat(message, model_config, api_key, session_id)
        return f"[MCP调用失败，使用基础模式] {fallback_response}"

@app.get("/api/chat-history")
async def get_chat_history():
    """获取聊天历史"""
    return configs.get("chat_history", [])

@app.delete("/api/chat-history")
async def clear_chat_history():
    """清空聊天历史"""
    configs["chat_history"] = []
    save_config()
    return {"message": "聊天历史已清空"}

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    # 计算健康服务器数量
    healthy_servers = sum(1 for status in mcp_server_status.values() if status.get("status") == "healthy")
    
    # 确定MCP实现类型
    mcp_implementation = "none"
    if MCP_STANDARD_AVAILABLE:
        mcp_implementation = "standard"
    elif MCP_DIRECT_AVAILABLE:
        mcp_implementation = "direct"
    
    return {
        "mcp_available": MCP_STANDARD_AVAILABLE or MCP_DIRECT_AVAILABLE,
        "mcp_implementation": mcp_implementation,
        "standard_mcp_available": MCP_STANDARD_AVAILABLE,
        "direct_mcp_available": MCP_DIRECT_AVAILABLE,
        "models_count": len(configs["models"]),
        "mcp_servers_count": len(configs["mcp_servers"]),
        "mcp_servers_healthy": healthy_servers,
        "chat_history_count": len(configs.get("chat_history", [])),
        "chat_sessions_count": len(configs.get("chat_sessions", {})),
        "supported_providers": list(PROVIDER_CONFIGS.keys()),
        "last_health_check": max([status.get("last_checked", "") for status in mcp_server_status.values()], default="")
    }

@app.post("/api/mcp-servers/debug-health-check")
async def debug_mcp_servers_health(request: MCPHealthCheckRequest):
    """调试版本的健康检查，提供更详细的错误信息"""
    results = {}
    
    servers_to_check = request.server_names if request.server_names else list(configs["mcp_servers"].keys())
    
    for server_name in servers_to_check:
        if server_name in configs["mcp_servers"]:
            config = configs["mcp_servers"][server_name]
            
            # 创建详细的调试信息
            debug_info = {
                "server_name": server_name,
                "config": config,
                "checks": []
            }
            
            try:
                # 检查连接类型
                connection_type = config.get("connection_type", "stdio")
                debug_info["checks"].append(f"✅ 连接类型: {connection_type}")
                
                if connection_type in ["sse", "streamableHttp"]:
                    url = config.get("url", "")
                    headers = config.get("headers", {})
                    timeout_seconds = config.get("timeout", 10)
                    
                    debug_info["checks"].append(f"🔗 目标URL: {url}")
                    debug_info["checks"].append(f"📋 请求头: {headers}")
                    debug_info["checks"].append(f"⏰ 超时设置: {timeout_seconds}秒")
                    
                    if not url:
                        debug_info["checks"].append("❌ 错误: 未指定URL")
                        results[server_name] = {
                            "status": "error",
                            "message": "No URL specified",
                            "debug": debug_info
                        }
                        continue
                    
                    # 确保URL是完整的SSE端点
                    sse_url = url if url.endswith('/sse') else f"{url.rstrip('/')}/sse"
                    debug_info["checks"].append(f"🔌 SSE端点URL: {sse_url}")
                    
                    # 尝试导入requests
                    try:
                        import requests
                        debug_info["checks"].append("✅ requests库可用")
                    except ImportError:
                        debug_info["checks"].append("❌ requests库未安装")
                        results[server_name] = {
                            "status": "error",
                            "message": "requests library not installed",
                            "debug": debug_info
                        }
                        continue
                    
                    # 尝试直接连接到SSE端点
                    start_time = time.time()
                    try:
                        debug_info["checks"].append(f"🚀 开始连接到SSE端点 {sse_url}...")
                        
                        # 添加正确的Accept头
                        headers_with_accept = headers.copy()
                        headers_with_accept['Accept'] = 'text/event-stream'
                        debug_info["checks"].append("📤 添加Accept: text/event-stream请求头")
                        
                        # 连接到SSE端点
                        response = requests.get(sse_url, headers=headers_with_accept, timeout=timeout_seconds, stream=True)
                        response_time = round((time.time() - start_time) * 1000, 2)
                        
                        debug_info["checks"].append(f"⏱️ 响应时间: {response_time}ms")
                        debug_info["checks"].append(f"📊 HTTP状态码: {response.status_code}")
                        debug_info["checks"].append(f"📄 响应头: {dict(response.headers)}")
                        
                        content_type = response.headers.get('content-type', '')
                        debug_info["checks"].append(f"📝 Content-Type: {content_type}")
                        
                        if response.status_code == 200:
                            if content_type.startswith('text/event-stream'):
                                debug_info["checks"].append("✅ 成功: 收到正确的SSE响应")
                                status = "healthy"
                                message = f"SSE端点可用 ({response_time}ms)"
                            else:
                                debug_info["checks"].append(f"⚠️ 警告: 状态码200但Content-Type不是text/event-stream")
                                status = "warning"
                                message = f"SSE端点返回200但Content-Type不正确: {content_type}"
                        elif response.status_code < 500:
                            debug_info["checks"].append(f"⚠️ 警告: HTTP {response.status_code}")
                            status = "warning"
                            message = f"SSE端点HTTP {response.status_code} (可能需要认证或参数)"
                        else:
                            debug_info["checks"].append(f"❌ 错误: HTTP {response.status_code} 服务器错误")
                            status = "error"
                            message = f"SSE端点服务器错误: HTTP {response.status_code}"
                            
                        # 无论状态如何，获取响应的开头部分进行分析
                        try:
                            # 尝试读取前几个字节来检查响应
                            first_chunk = next(response.iter_content(chunk_size=1024), None)
                            if first_chunk:
                                chunk_preview = first_chunk.decode('utf-8', errors='replace')[:200]
                                debug_info["checks"].append(f"📄 响应预览: {chunk_preview}")
                                
                                # 分析预览内容中是否包含SSE格式特征
                                if 'data:' in chunk_preview or 'event:' in chunk_preview:
                                    debug_info["checks"].append("✅ 检测到SSE格式特征 (data: 或 event:)")
                                    if status != "healthy":
                                        status = "warning"
                                        message = "Content-Type不正确但检测到SSE格式特征"
                        except Exception as chunk_error:
                            debug_info["checks"].append(f"⚠️ 无法读取响应内容: {str(chunk_error)}")
                        
                        # 关闭响应以防止连接保持开启
                        response.close()
                            
                    except requests.exceptions.Timeout:
                        debug_info["checks"].append(f"⏰ SSE连接超时 (>{timeout_seconds}秒)")
                        
                        # 尝试fallback到/health端点
                        debug_info["checks"].append("🔄 尝试连接健康检查端点...")
                        
                        # 构建健康检查URL
                        parsed_url = url.rstrip('/')
                        if parsed_url.endswith('/sse'):
                            base_url = parsed_url[:-4]  # 去掉'/sse'
                        else:
                            base_url = parsed_url
                        
                        health_url = f"{base_url}/health"
                        debug_info["checks"].append(f"🏥 健康检查URL: {health_url}")
                        
                        try:
                            health_response = requests.get(health_url, headers=headers, timeout=timeout_seconds)
                            debug_info["checks"].append(f"🏥 健康检查状态码: {health_response.status_code}")
                            
                            if health_response.status_code == 200:
                                debug_info["checks"].append("✅ 健康检查成功")
                                status = "warning"
                                message = f"SSE端点超时但健康检查成功，服务器可能运行正常"
                            else:
                                debug_info["checks"].append(f"❌ 健康检查失败: HTTP {health_response.status_code}")
                                status = "error"
                                message = f"SSE端点超时且健康检查返回HTTP {health_response.status_code}"
                        except Exception as health_error:
                            debug_info["checks"].append(f"❌ 健康检查失败: {str(health_error)}")
                            status = "error"
                            message = f"SSE连接超时且健康检查失败"
                            
                    except requests.exceptions.ConnectionError as e:
                        debug_info["checks"].append(f"❌ SSE连接失败: {str(e)}")
                        debug_info["checks"].append("💡 可能原因: 服务器未运行、端口不可达或网络问题")
                        status = "error"
                        message = f"无法连接到SSE端点 - 服务器可能未运行"
                    except Exception as e:
                        debug_info["checks"].append(f"❌ 未知错误: {str(e)}")
                        status = "error"
                        message = f"SSE检查出现未知错误: {str(e)}"
                    
                    results[server_name] = {
                        "status": status if 'status' in locals() else "error",
                        "message": message if 'message' in locals() else "健康检查失败",
                        "response_time": response_time if 'response_time' in locals() else None,
                        "last_checked": datetime.now().isoformat(),
                        "debug": debug_info
                    }
                    
            except Exception as e:
                debug_info["checks"].append(f"❌ 健康检查失败: {str(e)}")
                results[server_name] = {
                    "status": "error",
                    "message": f"Health check failed: {str(e)}",
                    "debug": debug_info
                }
    
    return results

@app.post("/api/mcp-servers/test-sse")
async def test_sse_functionality(request: dict):
    """测试SSE服务器的完整功能"""
    url = request.get('url')
    headers = request.get('headers', {})
    test_tool = request.get('tool', 'ping')
    test_params = request.get('params', {'test': 'health_check'})
    
    if not url:
        raise HTTPException(status_code=400, detail="需要提供URL")
    
    # 确保URL以/sse结尾
    sse_url = url if url.endswith('/sse') else f"{url.rstrip('/')}/sse"
    
    # Fallback: 使用基本requests实现简单测试
    try:
        # 准备测试
        test_results = {
            'connection': False,
            'request_sent': False,
            'events_received': [],
            'errors': [],
            'response_info': {}
        }
        
        # 准备请求头
        test_headers = headers.copy()
        test_headers['Accept'] = 'text/event-stream'
        
        # 1. 首先测试直接连接
        connection_start = time.time()
        try:
            # 使用短超时尝试连接
            response = requests.get(sse_url, headers=test_headers, stream=True, timeout=5)
            test_results['connection'] = True
            test_results['response_info'] = {
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'content_type': response.headers.get('content-type', ''),
                'connection_time_ms': round((time.time() - connection_start) * 1000, 1)
            }
            
            # 验证响应是否是SSE
            content_type = response.headers.get('content-type', '')
            if 'text/event-stream' in content_type:
                test_results['is_sse'] = True
            else:
                test_results['is_sse'] = False
                test_results['errors'].append(f"返回的Content-Type不是text/event-stream: {content_type}")
            
            # 2. 如果连接成功，尝试读取一些事件
            if response.status_code == 200:
                # 读取前5个事件或2秒，以先发生者为准
                event_timeout = time.time() + 2
                event_count = 0
                event_data = []
                
                # 迭代SSE流
                for line in response.iter_lines():
                    if time.time() > event_timeout or event_count >= 5:
                        break
                        
                    if line:
                        decoded_line = line.decode('utf-8')
                        if decoded_line.startswith('data:'):
                            event_data.append(decoded_line)
                            event_count += 1
                
                if event_data:
                    test_results['events_received'] = event_data
                    test_results['request_sent'] = True
                else:
                    test_results['errors'].append("连接成功但未收到事件数据")
            else:
                test_results['errors'].append(f"HTTP状态码: {response.status_code}")
                
            # 关闭响应
            response.close()
            
        except requests.exceptions.Timeout:
            test_results['errors'].append("连接超时")
        except requests.exceptions.ConnectionError as e:
            test_results['errors'].append(f"连接错误: {str(e)}")
        except Exception as e:
            test_results['errors'].append(f"未知错误: {str(e)}")
        
        # 3. 尝试发送一个实际请求（如果前面没成功）
        if not test_results.get('request_sent') and test_results.get('connection'):
            # 获取基础URL（去掉/sse）
            base_url = sse_url.rsplit('/sse', 1)[0]
            message_url = f"{base_url}/message"
            
            try:
                # 发送工具调用测试消息
                request_data = {
                    'tool': test_tool,
                    'params': test_params
                }
                message_response = requests.post(message_url, json=request_data, headers=headers, timeout=5)
                
                test_results['tool_request'] = {
                    'url': message_url,
                    'status_code': message_response.status_code,
                    'response': message_response.text[:200] if message_response.text else None
                }
                
                if message_response.status_code == 200:
                    test_results['request_sent'] = True
                else:
                    test_results['errors'].append(f"工具请求失败，状态码: {message_response.status_code}")
                    
            except Exception as e:
                test_results['errors'].append(f"发送工具请求失败: {str(e)}")
        
        # 生成结果摘要和建议
        success = test_results.get('connection') and not test_results.get('errors')
        summary = {
            'connection_successful': test_results.get('connection', False),
            'request_sent': test_results.get('request_sent', False),
            'events_count': len(test_results.get('events_received', [])),
            'errors_count': len(test_results.get('errors', [])),
            'is_sse': test_results.get('is_sse', False)
        }
        
        # 生成建议
        recommendations = []
        if not test_results.get('connection'):
            recommendations.append("❌ 无法建立SSE连接 - 请检查URL和服务器状态")
        elif not test_results.get('is_sse'):
            recommendations.append(f"❌ 端点返回非SSE响应 - Content-Type: {test_results.get('response_info', {}).get('content_type', '未知')}")
        elif not test_results.get('events_received'):
            recommendations.append("⚠️ 连接成功但未收到事件 - 检查服务器SSE实现")
        
        if test_results.get('errors'):
            for error in test_results.get('errors')[:3]:  # 只显示前3个错误
                recommendations.append(f"❌ 错误: {error}")
                
        if not recommendations:
            recommendations.append("✅ SSE连接测试成功")
            
        return {
            'success': success,
            'results': test_results,
            'summary': summary,
            'recommendations': recommendations
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'SSE测试失败: {str(e)}',
            'basic_check': None
        }

def _get_sse_recommendations(results: dict) -> list:
    """根据测试结果提供建议"""
    recommendations = []
    
    if not results.get('connection'):
        recommendations.append("❌ 无法建立SSE连接 - 检查服务器是否运行并支持SSE")
        
    if not results.get('request_sent'):
        recommendations.append("❌ 无法发送请求 - 检查消息端点是否可用")
        
    if len(results.get('events_received', [])) == 0:
        recommendations.append("⚠️ 未收到任何事件 - 检查服务器是否正确处理请求")
        
    if results.get('errors'):
        recommendations.append(f"❌ 发现 {len(results['errors'])} 个错误 - 检查服务器日志")
        
    health = results.get('health_check', {})
    if health.get('status') == 'error':
        recommendations.append("❌ 健康检查失败 - 服务器可能未正确配置")
    elif health.get('status') == 'warning':
        recommendations.append("⚠️ 健康检查有警告 - 检查content-type和响应格式")
        
    if not recommendations:
        recommendations.append("✅ SSE服务器运行正常")
        
    return recommendations

@app.get("/api/sessions")
async def get_sessions():
    """获取所有会话信息"""
    sessions = {}
    for session_id, messages in configs["chat_sessions"].items():
        # 为每个会话计算统计信息
        sessions[session_id] = {
            "message_count": len(messages),
            "last_updated": datetime.now().isoformat() if messages else None,
            "preview": messages[-1]["content"][:50] + "..." if len(messages) > 0 else "空会话"
        }
    return sessions

@app.get("/api/sessions/{session_id}")
async def get_session(session_id: str):
    """获取特定会话的内容"""
    session = SessionManager.get_session(session_id)
    return {
        "session_id": session_id,
        "messages": session,
        "message_count": len(session)
    }

@app.delete("/api/sessions/{session_id}")
async def clear_session(session_id: str):
    """清空特定会话"""
    SessionManager.clear_session(session_id)
    save_config()  # 保存配置，确保会话清除被持久化
    return {"message": f"会话 {session_id} 已清空"}

@app.delete("/api/sessions")
async def clear_all_sessions():
    """清空所有会话"""
    configs["chat_sessions"] = {}
    save_config()  # 保存配置
    return {"message": "所有会话已清空"}

@app.get("/api/mcp-tools/{server_name}")
async def get_mcp_tools(server_name: str):
    """获取指定MCP服务器提供的工具列表"""
    if not (MCP_STANDARD_AVAILABLE or MCP_DIRECT_AVAILABLE):
        return {
            "status": "error", 
            "message": "MCP功能不可用 - 请先安装MCP客户端", 
            "tools": []
        }
        
    if server_name not in configs["mcp_servers"]:
        raise HTTPException(status_code=404, detail=f"MCP服务器 {server_name} 不存在")
    
    try:
        # 首先检查服务器健康状态
        server_health = mcp_server_status.get(server_name, {})
        if server_health.get("status") == "error":
            return {
                "status": "error",
                "message": f"服务器 {server_name} 状态异常: {server_health.get('message', '未知错误')}",
                "tools": []
            }
        
        # 创建客户端
        client = gen_client(server_name)
        
        # 获取工具列表
        try:
            tools = await client.get_tools()
            
            # 确保返回的工具是一个列表
            if not isinstance(tools, list):
                logger.warning(f"服务器 {server_name} 返回的工具不是列表: {type(tools)}")
                tools = []
                
            # 如果工具列表为空，提供一个更友好的警告
            if len(tools) == 0:
                return {
                    "status": "warning",
                    "message": f"服务器 {server_name} 没有提供任何工具",
                    "tools": []
                }
                
            return {
                "status": "success",
                "server": server_name,
                "tools": tools
            }
        except AttributeError as e:
            logger.error(f"get_tools方法不可用: {str(e)}")
            return {
                "status": "error",
                "message": f"获取工具列表失败: 客户端对象没有get_tools方法，可能是MCP版本不兼容",
                "tools": [],
                "debug_info": {
                    "error_type": "AttributeError",
                    "error_message": str(e),
                    "client_type": type(client).__name__,
                    "client_methods": [m for m in dir(client) if not m.startswith('_') and callable(getattr(client, m))]
                }
            }
    except Exception as e:
        logger.error(f"获取MCP工具列表失败: {str(e)}")
        import traceback
        error_traceback = traceback.format_exc()
        
        return {
            "status": "error",
            "message": f"获取工具列表失败: {str(e)}",
            "tools": [],
            "debug_info": {
                "error_type": type(e).__name__,
                "error_traceback": error_traceback,
                "server_name": server_name,
                "server_config": configs["mcp_servers"].get(server_name)
            }
        }

@app.post("/api/mcp-tools/{server_name}/call")
async def call_mcp_tool(server_name: str, request: dict):
    """调用指定MCP服务器的工具"""
    if not (MCP_STANDARD_AVAILABLE or MCP_DIRECT_AVAILABLE):
        return {
            "status": "error", 
            "message": "MCP功能不可用 - 请先安装MCP客户端"
        }
        
    if server_name not in configs["mcp_servers"]:
        raise HTTPException(status_code=404, detail=f"MCP服务器 {server_name} 不存在")
    
    if "tool" not in request:
        raise HTTPException(status_code=400, detail="未指定工具名称")
        
    if "params" not in request:
        raise HTTPException(status_code=400, detail="未提供工具参数")
    
    try:
        tool_name = request["tool"]
        params = request["params"]
        
        # 创建客户端
        client = gen_client(server_name)
        
        # 调用工具
        try:
            result = await client.call_tool(tool_name, params)
            
            return {
                "status": "success",
                "server": server_name,
                "tool": tool_name,
                "result": result
            }
        except AttributeError as e:
            logger.error(f"call_tool方法不可用: {str(e)}")
            return {
                "status": "error",
                "message": f"调用工具失败: 客户端对象没有call_tool方法，可能是MCP版本不兼容"
            }
    except Exception as e:
        logger.error(f"调用MCP工具失败: {str(e)}")
        return {
            "status": "error",
            "message": f"调用工具失败: {str(e)}"
        }

def gen_client(server_name):
    """Create a MCP client for the given server name"""
    # Check if the server exists in our configs
    if server_name not in configs["mcp_servers"]:
        logger.warning(f"Server {server_name} not found in configs")
        return DummyClient(server_name)
        
    # Get the server config
    server_config = configs["mcp_servers"][server_name]
    
    # Try using the standard MCP client
    if MCP_STANDARD_AVAILABLE:
        try:
            logger.info(f"Creating standard MCP client for server {server_name}")
            return create_standard_client(server_name, server_config)
        except Exception as e:
            logger.error(f"Error creating standard MCP client: {e}")
            # Fall through to direct client
    
    # Then try using our direct implementation
    if MCP_DIRECT_AVAILABLE:
        try:
            logger.info(f"Creating direct MCP client for server {server_name}")
            return create_direct_client(server_name, server_config)
        except Exception as e:
            logger.error(f"Error creating direct MCP client: {e}")
    
    # Fall back to dummy client
    logger.warning(f"All MCP client implementations failed for server {server_name}. Using dummy client.")
    return DummyClient(server_name)

# 直接处理MCP聊天，不依赖mcp-agent
async def direct_mcp_chat(message: str, model_config: dict, api_key: str, mcp_servers: List[str], session_id: str = "default") -> str:
    """使用直接的MCP客户端处理聊天，无需依赖mcp-agent包"""
    provider = model_config["provider"]
    model_name = model_config.get("model_name", "gpt-4")
    
    # 获取会话上下文
    chat_history = SessionManager.get_session(session_id)
    
    # 先保存用户消息到会话
    SessionManager.add_message(session_id, "user", message)
    
    try:
        # 设置环境变量
        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
            os.environ["OPENAI_API_KEY"] = api_key
            # 对于兼容OpenAI API的服务，需要设置base_url
            if model_config.get("api_url"):
                os.environ["OPENAI_BASE_URL"] = model_config["api_url"]
            
            # 使用OpenAI API
            try:
                import openai
                openai.api_key = api_key
                if model_config.get("api_url"):
                    openai.api_base = model_config["api_url"]
            except ImportError:
                logger.error("OpenAI库未安装")
                raise ValueError("OpenAI库未安装，请运行 pip install openai")
                
        elif provider == "anthropic":
            os.environ["ANTHROPIC_API_KEY"] = api_key
            if model_config.get("api_url"):
                os.environ["ANTHROPIC_BASE_URL"] = model_config["api_url"]
                
            # 使用Anthropic API
            try:
                import anthropic
            except ImportError:
                logger.error("Anthropic库未安装")
                raise ValueError("Anthropic库未安装，请运行 pip install anthropic")
        else:
            raise ValueError(f"不支持的提供商: {provider}")
        
        # 获取所有可用MCP服务器的工具列表
        tools_info = []
        for server_name in mcp_servers:
            try:
                if server_name not in configs["mcp_servers"]:
                    continue
                    
                # 检查服务器健康状态
                server_health = mcp_server_status.get(server_name, {})
                if server_health.get("status") == "error":
                    logger.warning(f"服务器 {server_name} 状态异常，跳过工具获取")
                    continue
                
                # 获取工具列表
                client = gen_client(server_name)
                try:
                    tools = await client.get_tools()
                    if not isinstance(tools, list):
                        logger.warning(f"从服务器 {server_name} 获取到的工具不是列表: {type(tools)}")
                        tools = []
                    
                    server_tools = {
                        "server_name": server_name,
                        "tools": tools
                    }
                    tools_info.append(server_tools)
                    
                    logger.info(f"从服务器 {server_name} 获取到 {len(tools)} 个工具")
                except Exception as e:
                    logger.error(f"从服务器 {server_name} 获取工具失败: {str(e)}")
                    continue
            except Exception as e:
                logger.error(f"处理服务器 {server_name} 时出错: {str(e)}")
        
        # 构建工具描述
        tools_description = ""
        for server_tool in tools_info:
            server_name = server_tool["server_name"]
            tools = server_tool["tools"]
            
            if tools:
                tools_description += f"\n服务器 {server_name} 提供的工具:\n"
                for tool in tools:
                    tool_name = tool.get("name", "未知工具")
                    tool_description = tool.get("description", "无描述")
                    params_info = tool.get("params", {})
                    
                    tools_description += f"- {tool_name}: {tool_description}\n"
                    if params_info:
                        tools_description += f"  参数: {json.dumps(params_info, ensure_ascii=False)}\n"
        
        # 准备系统提示
        system_instruction = f"""你是一个有用的AI助手，具有使用各种工具和服务的能力。

当前可用的MCP服务器工具：{', '.join(mcp_servers) if mcp_servers else '无'}

{tools_description}

请根据用户的问题提供准确和有帮助的回答。根据需要主动使用上述工具，以便更好地回答用户问题。

使用工具时，需要明确：
1. 使用哪个服务器的工具
2. 工具名称
3. 工具所需的参数
4. 工具所需的参数参数不能添加任何注释

当你需要使用工具时，请按照以下格式：
```
我需要使用[工具名称]工具来[目的]。
服务器: [服务器名称]
工具: [工具名称]
参数:
  参数1: 值1
  参数2: 值2
```

执行上述工具调用后，你会收到结果，然后你可以使用这些结果来回答我的问题。
"""

        # 准备聊天历史上下文，只取最近10轮对话
        context_history = chat_history[-10:] if len(chat_history) > 10 else chat_history
        context = "\n\n".join([
            f"{msg['role'].capitalize()}: {msg['content']}" 
            for msg in context_history[:-1]  # 不包括最新的用户消息，因为会单独传入
        ])
        
        # 如果有聊天历史，添加到消息前面
        if context:
            full_message = f"以下是之前的对话历史：\n\n{context}\n\n现在请回答我的新问题：{message}"
        else:
            full_message = message
        
        # 准备完整的聊天历史记录用于LLM输入
        messages = []
        messages.append({"role": "system", "content": system_instruction})
        
        for msg in context_history[:-1]:  # 不包括最新的用户消息
            messages.append({"role": msg["role"], "content": msg["content"]})
            
        messages.append({"role": "user", "content": message})
        
        # 执行对话
        logger.info(f"使用模型 {model_name} 和 MCP 服务器: {', '.join(mcp_servers)}")
        
        response_text = ""
        needs_tool = False
        tool_pattern = "我需要使用"
        
        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
            # OpenAI API调用
            if model_config.get("api_url"):
                client = openai.OpenAI(api_key=api_key, base_url=model_config["api_url"])
            else:
                client = openai.OpenAI(api_key=api_key)
                
            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                max_tokens=2000,
                temperature=0.7
            )
            response_text = response.choices[0].message.content
            
        elif provider == "anthropic":
            # Anthropic API调用
            client = anthropic.Anthropic(api_key=api_key)
            response = client.messages.create(
                model=model_name,
                max_tokens=2000,
                messages=messages
            )
            response_text = response.content[0].text
        
        # 检查回复是否需要使用工具
        if tool_pattern in response_text:
            # 解析LLM的工具调用请求
            needs_tool = True
            
            # 尝试提取工具信息
            try:
                # 提取格式化的工具调用块
                import re
                tool_block_pattern = r"服务器:\s*([^\n]+)\n工具:\s*([^\n]+)\n参数:([\s\S]+?)(?=```|$)"
                matches = re.search(tool_block_pattern, response_text)
                
                if matches:
                    server_name = matches.group(1).strip()
                    tool_name = matches.group(2).strip()
                    params_text = matches.group(3).strip()
                    
                    # 解析参数
                    params = {}
                    param_pattern = r"(\w+):\s*(.+)(?:\n|$)"
                    param_matches = re.findall(param_pattern, params_text)
                    
                    for param_name, param_value in param_matches:
                        params[param_name.strip()] = param_value.strip()
                    
                    # 检查服务器是否存在
                    if server_name not in configs["mcp_servers"]:
                        response_text += f"\n\n[错误: 服务器 '{server_name}' 不存在]"
                    else:
                        # 调用工具
                        client = gen_client(server_name)
                        tool_result = await client.call_tool(tool_name, params)
                        
                        # 将工具结果添加到会话
                        tool_result_text = f"\n\n工具调用结果：\n```\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}\n```\n\n"
                        
                        # 现在让LLM处理工具结果
                        messages.append({"role": "assistant", "content": response_text})
                        messages.append({"role": "user", "content": f"工具已被调用，这是结果：{tool_result_text}\n请基于这个结果回答我的问题。"})
                        
                        # 再次调用LLM
                        if provider == "openai" or provider in ["deepseek", "qwen", "zhipu", "moonshot"]:
                            if model_config.get("api_url"):
                                client = openai.OpenAI(api_key=api_key, base_url=model_config["api_url"])
                            else:
                                client = openai.OpenAI(api_key=api_key)
                                
                            final_response = client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=2000,
                                temperature=0.7
                            )
                            final_text = final_response.choices[0].message.content
                        elif provider == "anthropic":
                            final_response = client.messages.create(
                                model=model_name,
                                max_tokens=2000,
                                messages=messages
                            )
                            final_text = final_response.content[0].text
                        
                        # 组合最终响应
                        response_text = f"{response_text}\n\n{final_text}"
                else:
                    response_text += "\n\n[无法解析工具调用格式]"
            except Exception as e:
                logger.error(f"工具调用失败: {str(e)}")
                response_text += f"\n\n[工具调用失败: {str(e)}]"
        
        # 保存助手回复到会话
        SessionManager.add_message(session_id, "assistant", response_text)
        
        return response_text
        
    except Exception as e:
        logger.error(f"直接MCP聊天错误: {str(e)}")
        # 如果MCP调用失败，回退到简单聊天
        fallback_response = await simple_chat(message, model_config, api_key, session_id)
        return f"[MCP调用失败，使用基础模式] {fallback_response}"

# 添加知识库相关的路由
@app.post("/api/knowledge-base/upload")
async def upload_document(file: UploadFile):
    """上传文档到知识库"""
    try:
        # 确保目录存在
        os.makedirs("kb_documents", exist_ok=True)
        
        # 保存文件
        file_path = os.path.join("kb_documents", file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
            
        # 处理文档（向量化等）
        from kb_manager import KnowledgeBaseManager
        kb_manager = KnowledgeBaseManager()
        processed_files = kb_manager.load_documents()
        
        return JSONResponse({
            "status": "success",
            "message": f"文件上传成功: {file.filename}",
            "processed_files": processed_files
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/knowledge-base/documents")
async def list_documents():
    """列出知识库中的所有文档"""
    try:
        documents_dir = "kb_documents"
        if not os.path.exists(documents_dir):
            return []
        
        files = []
        for filename in os.listdir(documents_dir):
            file_path = os.path.join(documents_dir, filename)
            if os.path.isfile(file_path):
                files.append({
                    "name": filename,
                    "size": os.path.getsize(file_path),
                    "created": datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                })
        return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/knowledge-base/documents/{filename}")
async def delete_document(filename: str):
    """删除知识库中的文档"""
    try:
        file_path = os.path.join("kb_documents", filename)
        if os.path.exists(file_path):
            os.remove(file_path)
            return {"status": "success", "message": f"文件删除成功: {filename}"}
        else:
            raise HTTPException(status_code=404, detail=f"文件不存在: {filename}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    import socket
    import sys
    
    def find_free_port(start_port=8001, max_attempts=10):
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return None
    
    # 检查并安装缺失的依赖
    try:
        import aiohttp
    except ImportError:
        print("Installing aiohttp for advanced SSE health checks...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "aiohttp"])
        print("aiohttp installed successfully!")
    
    # 查找可用端口
    port = find_free_port()
    if port is None:
        print("Error: Could not find available port in range 8001-8010")
        sys.exit(1)
    
    print("🚀 Starting AI Chat with MCP...")
    print(f"📊 MCP功能可用: {'✅' if MCP_AVAILABLE else '❌'}")
    print(f"🌐 服务器端口: {port}")
    print(f"🔗 访问地址: http://localhost:{port}")
    print(f"💾 配置文件: {os.path.abspath('config.yaml')}")
    print("=" * 50)
    
    if port != 8001:
        print(f"⚠️  注意: 使用端口 {port} 而不是默认的 8001")
    
    uvicorn.run(app, host="127.0.0.1", port=port)

# ==================== 商业化API端点 ====================

if BUSINESS_AVAILABLE:
    @app.get("/api/business/dashboard/stats")
    async def get_dashboard_stats():
        """获取仪表板统计数据"""
        db = auth_service.get_db()
        try:
            stats = dashboard_service.get_dashboard_stats(db)
            return stats
        finally:
            db.close()

    @app.get("/api/business/analytics/users")
    async def get_user_analytics(days: int = 30):
        """获取用户分析数据"""
        db = auth_service.get_db()
        try:
            analytics = dashboard_service.get_user_analytics(db, days)
            return analytics
        finally:
            db.close()

    @app.get("/api/business/analytics/usage")
    async def get_usage_analytics(days: int = 30):
        """获取使用分析数据"""
        db = auth_service.get_db()
        try:
            analytics = dashboard_service.get_usage_analytics(db, days)
            return analytics
        finally:
            db.close()

    @app.get("/api/business/analytics/revenue")
    async def get_revenue_analytics(months: int = 12):
        """获取收入分析数据"""
        db = auth_service.get_db()
        try:
            analytics = dashboard_service.get_revenue_analytics(db, months)
            return analytics
        finally:
            db.close()

    @app.get("/api/business/plans")
    async def get_plans():
        """获取所有套餐"""
        db = auth_service.get_db()
        try:
            plans = billing_service.get_plans(db)
            return plans
        finally:
            db.close()

    @app.post("/api/business/auth/register")
    async def register_user(user_data: UserCreate):
        """用户注册"""
        db = auth_service.get_db()
        try:
            user = auth_service.create_user(user_data, db)
            return {"message": "用户注册成功", "user_id": user.id}
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        finally:
            db.close()

    @app.post("/api/business/auth/login")
    async def login_user(login_data: UserLogin):
        """用户登录"""
        db = auth_service.get_db()
        try:
            user = auth_service.authenticate_user(login_data.email, login_data.password, db)
            if not user:
                raise HTTPException(status_code=401, detail="邮箱或密码错误")

            # 创建访问令牌
            access_token = auth_service.create_access_token({"sub": str(user.id)})
            refresh_token = auth_service.create_refresh_token({"sub": str(user.id)})

            return Token(
                access_token=access_token,
                refresh_token=refresh_token
            )
        finally:
            db.close()
