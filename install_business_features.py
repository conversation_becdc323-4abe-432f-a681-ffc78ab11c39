#!/usr/bin/env python3
"""
商业化功能安装脚本
自动安装和配置企业级功能
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_banner():
    """显示安装横幅"""
    print("=" * 60)
    print("🚀 IntelliHub Pro - 企业级功能安装")
    print("=" * 60)
    print()

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    logger.info(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info("✅ Python版本检查通过")
    return True

def install_dependencies():
    """安装商业化功能依赖"""
    logger.info("📦 安装商业化功能依赖...")
    
    business_deps = [
        "bcrypt>=4.0.1",
        "PyJWT>=2.8.0", 
        "email-validator>=2.0.0",
        "python-jose[cryptography]>=3.3.0",
        "sqlalchemy>=2.0.9",
        "aiosqlite>=0.18.0"
    ]
    
    failed_packages = []
    
    for package in business_deps:
        try:
            logger.info(f"安装 {package}...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True, text=True, check=True
            )
            logger.info(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ {package} 安装失败: {e}")
            failed_packages.append(package)
    
    return failed_packages

def create_database():
    """创建数据库表"""
    logger.info("🗄️ 创建数据库表...")
    
    try:
        # 导入并初始化数据库
        from auth_system import Base, engine
        from billing_system import Plan, Subscription, Invoice, UsageQuota
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
        
        # 初始化默认数据
        from billing_system import billing_service
        logger.info("✅ 默认套餐初始化成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库创建失败: {e}")
        return False

def create_config_files():
    """创建配置文件"""
    logger.info("⚙️ 创建配置文件...")
    
    # 创建环境变量配置文件
    env_content = """# IntelliHub Pro 环境配置

# 数据库配置
DATABASE_URL=sqlite:///./business.db

# JWT配置
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 应用配置
APP_NAME=IntelliHub Pro
APP_VERSION=1.0.0
DEBUG=false

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# 支付配置（可选）
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
"""
    
    try:
        with open('.env.business', 'w', encoding='utf-8') as f:
            f.write(env_content)
        logger.info("✅ 环境配置文件创建成功: .env.business")
        
        # 创建业务配置文件
        business_config = {
            "plans": {
                "free": {
                    "name": "免费版",
                    "price": 0,
                    "features": ["基础聊天", "1个模型", "基础记忆"]
                },
                "pro": {
                    "name": "专业版", 
                    "price": 99,
                    "features": ["多模型", "高级记忆", "API访问"]
                }
            },
            "limits": {
                "free": {
                    "api_calls_monthly": 1000,
                    "storage_gb": 1,
                    "users": 1
                },
                "pro": {
                    "api_calls_monthly": 10000,
                    "storage_gb": 10,
                    "users": 5
                }
            }
        }
        
        import json
        with open('business_config.json', 'w', encoding='utf-8') as f:
            json.dump(business_config, f, indent=2, ensure_ascii=False)
        logger.info("✅ 业务配置文件创建成功: business_config.json")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件创建失败: {e}")
        return False

def test_business_features():
    """测试商业化功能"""
    logger.info("🧪 测试商业化功能...")
    
    try:
        # 测试认证系统
        from auth_system import auth_service
        logger.info("✅ 认证系统导入成功")
        
        # 测试计费系统
        from billing_system import billing_service
        logger.info("✅ 计费系统导入成功")
        
        # 测试仪表板系统
        from dashboard_system import dashboard_service
        logger.info("✅ 仪表板系统导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 功能测试失败: {e}")
        return False

def update_main_navigation():
    """更新主导航，添加商业化入口"""
    logger.info("🔗 更新主导航...")
    
    try:
        # 读取主页模板
        index_file = Path("templates/index.html")
        if not index_file.exists():
            logger.warning("主页模板不存在，跳过导航更新")
            return True
        
        with open(index_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经添加了商业化导航
        if 'href="/business"' in content:
            logger.info("✅ 商业化导航已存在")
            return True
        
        # 在AI记忆系统后添加商业化导航
        cognee_nav = '''<button onclick="window.location.href='/cognee'" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-indigo-50 hover:text-indigo-600 transition-colors" data-item="cognee">
                    <i class="fas fa-brain mr-3 text-indigo-500"></i>
                    <span class="font-medium">AI记忆系统</span>
                </button>'''
        
        business_nav = '''<button onclick="window.location.href='/business'" class="sidebar-item w-full flex items-center px-3 py-2 text-left text-gray-700 rounded hover:bg-green-50 hover:text-green-600 transition-colors" data-item="business">
                    <i class="fas fa-chart-line mr-3 text-green-500"></i>
                    <span class="font-medium">企业仪表板</span>
                </button>'''
        
        # 替换内容
        new_content = content.replace(cognee_nav, cognee_nav + '\n                ' + business_nav)
        
        # 写回文件
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        logger.info("✅ 主导航更新成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 导航更新失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 商业化功能安装完成!")
    print()
    print("📋 后续步骤:")
    print("1. 启动应用: python app.py")
    print("2. 访问企业仪表板: http://localhost:8000/business")
    print("3. 配置环境变量: 编辑 .env.business 文件")
    print("4. 设置支付网关: 配置 Stripe 密钥（可选）")
    print()
    print("🔧 配置文件:")
    print("- 环境配置: .env.business")
    print("- 业务配置: business_config.json")
    print("- 数据库: business.db")
    print()
    print("📚 功能说明:")
    print("- 用户认证和授权")
    print("- 多租户组织管理")
    print("- 订阅和计费系统")
    print("- 使用量统计和配额")
    print("- 企业级仪表板")
    print("- API密钥管理")
    print()
    print("💡 提示:")
    print("- 首次运行会自动创建管理员账户")
    print("- 可以通过API进行用户注册和登录")
    print("- 支持多种套餐和定价模式")

def main():
    """主安装流程"""
    print_banner()
    
    # 1. 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 2. 安装依赖
    failed_packages = install_dependencies()
    if failed_packages:
        logger.error(f"以下包安装失败: {', '.join(failed_packages)}")
        logger.error("请手动安装这些包或检查网络连接")
        sys.exit(1)
    
    # 3. 创建数据库
    if not create_database():
        logger.error("数据库创建失败")
        sys.exit(1)
    
    # 4. 创建配置文件
    if not create_config_files():
        logger.error("配置文件创建失败")
        sys.exit(1)
    
    # 5. 测试功能
    if not test_business_features():
        logger.error("功能测试失败")
        sys.exit(1)
    
    # 6. 更新导航
    update_main_navigation()
    
    # 7. 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出安装")
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        logger.error(f"安装失败: {e}")
