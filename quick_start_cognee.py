#!/usr/bin/env python3
"""
Cognee快速启动脚本
帮助用户快速配置和启动Cognee功能
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_banner():
    """显示欢迎横幅"""
    print("=" * 60)
    print("🧠 Cognee AI记忆系统 - 快速启动")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖项"""
    print("📦 检查依赖项...")
    
    try:
        import cognee
        print("✅ cognee 已安装")
        return True
    except ImportError:
        print("❌ cognee 未安装")
        return False

def install_dependencies():
    """安装依赖项"""
    print("🔧 安装Cognee依赖...")
    
    try:
        result = subprocess.run([sys.executable, "install_cognee.py"], 
                              capture_output=True, text=True, check=True)
        print("✅ 依赖安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请手动运行: python install_cognee.py")
        return False
    except FileNotFoundError:
        print("❌ 找不到install_cognee.py文件")
        return False

def get_api_key():
    """获取API密钥"""
    print("🔑 配置API密钥...")
    
    # 检查环境变量
    existing_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
    if existing_key:
        print("✅ 找到现有的API密钥")
        return existing_key
    
    print("未找到API密钥，请输入您的OpenAI API密钥:")
    print("(格式: sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx)")
    
    while True:
        api_key = input("API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空")
            continue
        
        if not api_key.startswith('sk-'):
            print("⚠️ API密钥格式可能不正确，但继续使用")
        
        # 设置环境变量
        os.environ['LLM_API_KEY'] = api_key
        os.environ['OPENAI_API_KEY'] = api_key
        
        print("✅ API密钥已设置")
        return api_key

def test_cognee():
    """测试Cognee功能"""
    print("🧪 测试Cognee功能...")
    
    try:
        from cognee_service import cognee_service
        
        # 使用简化初始化
        success = cognee_service.initialize_without_test(
            os.getenv('LLM_API_KEY'), 
            "openai"
        )
        
        if success:
            print("✅ Cognee服务初始化成功")
            return True
        else:
            print("❌ Cognee服务初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_sample_data():
    """创建示例数据"""
    print("📝 创建示例数据...")
    
    try:
        from cognee_service import cognee_service
        import asyncio
        
        async def add_samples():
            samples = [
                "Python是一种高级编程语言，以其简洁和可读性而闻名。",
                "机器学习是人工智能的一个分支，使计算机能够从数据中学习。",
                "Cognee是一个为AI代理提供动态记忆的开源系统。"
            ]
            
            for i, text in enumerate(samples, 1):
                success = await cognee_service.add_text(text, {
                    "source": "quick_start",
                    "sample_id": i
                })
                if success:
                    print(f"  ✅ 示例 {i} 添加成功")
                else:
                    print(f"  ❌ 示例 {i} 添加失败")
            
            # 生成知识图谱
            print("🧠 生成知识图谱...")
            success = await cognee_service.cognify()
            if success:
                print("✅ 知识图谱生成成功")
            else:
                print("❌ 知识图谱生成失败")
        
        asyncio.run(add_samples())
        return True
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("🚀 启动服务器...")
    print("请在新的终端窗口中运行以下命令:")
    print("  python app.py")
    print()
    print("然后访问: http://localhost:8000/cognee")
    print()
    
    # 询问是否自动启动
    try:
        choice = input("是否现在启动服务器? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("正在启动服务器...")
            subprocess.Popen([sys.executable, "app.py"])
            print("✅ 服务器已在后台启动")
            return True
    except KeyboardInterrupt:
        print("\n用户取消")
    
    return False

def show_next_steps():
    """显示后续步骤"""
    print("🎉 快速启动完成!")
    print()
    print("📋 后续步骤:")
    print("1. 访问 http://localhost:8000/cognee")
    print("2. 如果需要，在界面中重新配置API密钥")
    print("3. 尝试添加文本或上传文件")
    print("4. 使用搜索功能测试记忆系统")
    print()
    print("📖 更多信息:")
    print("- 详细文档: COGNEE_INTEGRATION.md")
    print("- 故障排除: COGNEE_TROUBLESHOOTING.md")
    print("- 功能演示: python demo_cognee.py")
    print()

def main():
    """主流程"""
    print_banner()
    
    # 1. 检查依赖
    if not check_dependencies():
        print("正在安装依赖...")
        if not install_dependencies():
            print("❌ 依赖安装失败，请手动安装")
            return
    
    # 2. 配置API密钥
    api_key = get_api_key()
    if not api_key:
        print("❌ 未配置API密钥，无法继续")
        return
    
    # 3. 测试Cognee
    if not test_cognee():
        print("⚠️ Cognee测试失败，但可以继续")
        print("建议在Web界面中使用'跳过功能测试'选项")
    
    # 4. 创建示例数据（可选）
    try:
        choice = input("是否创建示例数据? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            create_sample_data()
    except KeyboardInterrupt:
        print("\n跳过示例数据创建")
    
    # 5. 启动服务器
    start_server()
    
    # 6. 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请查看 COGNEE_TROUBLESHOOTING.md 获取帮助")
