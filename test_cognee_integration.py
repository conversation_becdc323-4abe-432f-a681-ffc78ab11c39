#!/usr/bin/env python3
"""
Cognee集成测试脚本
验证cognee服务是否正常工作
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_cognee_service():
    """测试cognee服务功能"""
    try:
        # 导入cognee服务
        from cognee_service import cognee_service, COGNEE_AVAILABLE
        
        if not COGNEE_AVAILABLE:
            logger.error("❌ Cognee不可用，请先安装cognee库")
            return False
        
        logger.info("✅ Cognee库导入成功")
        
        # 检查服务状态
        status = cognee_service.get_status()
        logger.info(f"📊 服务状态: {status}")
        
        # 如果未初始化，尝试初始化（需要API密钥）
        if not status['initialized']:
            logger.warning("⚠️ Cognee服务未初始化")
            
            # 检查环境变量中的API密钥
            api_key = os.getenv('LLM_API_KEY') or os.getenv('OPENAI_API_KEY')
            if api_key:
                logger.info("🔑 找到API密钥，尝试初始化...")
                success = await cognee_service.initialize(api_key, "openai")
                if success:
                    logger.info("✅ Cognee服务初始化成功")
                else:
                    logger.error("❌ Cognee服务初始化失败")
                    return False
            else:
                logger.warning("⚠️ 未找到API密钥，跳过初始化测试")
                logger.info("💡 请设置环境变量 LLM_API_KEY 或 OPENAI_API_KEY")
                return True  # 不算失败，只是没有API密钥
        
        # 测试添加文本
        test_text = "这是一个测试文本，用于验证cognee集成是否正常工作。"
        logger.info("📝 测试添加文本...")
        
        success = await cognee_service.add_text(test_text, {
            "source": "test_script",
            "test_type": "integration_test"
        })
        
        if success:
            logger.info("✅ 文本添加成功")
        else:
            logger.error("❌ 文本添加失败")
            return False
        
        # 测试cognify
        logger.info("🧠 测试知识图谱生成...")
        success = await cognee_service.cognify()
        
        if success:
            logger.info("✅ 知识图谱生成成功")
        else:
            logger.error("❌ 知识图谱生成失败")
            return False
        
        # 测试搜索
        logger.info("🔍 测试搜索功能...")
        results = await cognee_service.search("测试", limit=3)
        
        if results:
            logger.info(f"✅ 搜索成功，找到 {len(results)} 条结果")
            for i, result in enumerate(results):
                logger.info(f"  结果 {i+1}: {result['content'][:50]}...")
        else:
            logger.warning("⚠️ 搜索未返回结果（可能是正常的）")
        
        # 测试对话保存
        logger.info("💬 测试对话保存...")
        success = await cognee_service.add_conversation(
            "你好，这是一个测试消息",
            "你好！我是AI助手，很高兴为您服务。",
            "test_session"
        )
        
        if success:
            logger.info("✅ 对话保存成功")
        else:
            logger.error("❌ 对话保存失败")
            return False
        
        logger.info("🎉 所有测试通过！Cognee集成正常工作")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入错误: {e}")
        logger.error("请确保已安装cognee及其依赖")
        return False
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        return False

async def test_api_endpoints():
    """测试API端点"""
    try:
        import requests
        import time
        
        # 等待服务启动
        base_url = "http://localhost:8000"
        
        logger.info("🌐 测试API端点...")
        
        # 测试状态端点
        try:
            response = requests.get(f"{base_url}/api/cognee/status", timeout=5)
            if response.status_code == 200:
                logger.info("✅ 状态API端点正常")
                status_data = response.json()
                logger.info(f"📊 API状态: {status_data}")
            else:
                logger.warning(f"⚠️ 状态API返回状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ 无法连接到API端点: {e}")
            logger.info("💡 请确保应用正在运行 (python app.py)")
            return False
        
        # 测试其他端点（如果服务已初始化）
        if status_data.get('initialized'):
            # 测试搜索端点
            try:
                search_data = {
                    "query": "测试",
                    "limit": 3
                }
                response = requests.post(f"{base_url}/api/cognee/search", 
                                       json=search_data, timeout=10)
                if response.status_code == 200:
                    logger.info("✅ 搜索API端点正常")
                    search_results = response.json()
                    logger.info(f"🔍 搜索结果数量: {search_results.get('count', 0)}")
                else:
                    logger.warning(f"⚠️ 搜索API返回状态码: {response.status_code}")
            except requests.exceptions.RequestException as e:
                logger.warning(f"⚠️ 搜索API测试失败: {e}")
        
        return True
        
    except ImportError:
        logger.warning("⚠️ requests库未安装，跳过API测试")
        return True
    except Exception as e:
        logger.error(f"❌ API测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    logger.info("📁 检查文件结构...")
    
    required_files = [
        "cognee_service.py",
        "templates/cognee_interface.html",
        "COGNEE_INTEGRATION.md",
        "install_cognee.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        logger.info("✅ 所有必需文件都存在")
        return True

def test_dependencies():
    """测试依赖项"""
    logger.info("📦 检查依赖项...")
    
    dependencies = [
        "cognee",
        "neo4j", 
        "chromadb",
        "sentence_transformers",
        "networkx"
    ]
    
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep)
            logger.info(f"✅ {dep} 已安装")
        except ImportError:
            missing_deps.append(dep)
            logger.warning(f"⚠️ {dep} 未安装")
    
    if missing_deps:
        logger.error(f"❌ 缺少依赖: {missing_deps}")
        logger.info("💡 运行 'python install_cognee.py' 安装缺少的依赖")
        return False
    else:
        logger.info("✅ 所有依赖都已安装")
        return True

async def main():
    """主测试流程"""
    logger.info("🚀 开始Cognee集成测试...")
    
    # 测试文件结构
    if not test_file_structure():
        logger.error("❌ 文件结构测试失败")
        sys.exit(1)
    
    # 测试依赖项
    if not test_dependencies():
        logger.error("❌ 依赖项测试失败")
        sys.exit(1)
    
    # 测试cognee服务
    if not await test_cognee_service():
        logger.error("❌ Cognee服务测试失败")
        sys.exit(1)
    
    # 测试API端点
    if not await test_api_endpoints():
        logger.warning("⚠️ API端点测试失败（可能是服务未运行）")
    
    logger.info("🎉 所有测试完成！")
    logger.info("")
    logger.info("📋 测试总结:")
    logger.info("✅ 文件结构正常")
    logger.info("✅ 依赖项完整")
    logger.info("✅ Cognee服务功能正常")
    logger.info("")
    logger.info("🚀 下一步:")
    logger.info("1. 启动应用: python app.py")
    logger.info("2. 访问 http://localhost:8000/cognee")
    logger.info("3. 配置API密钥并开始使用")

if __name__ == "__main__":
    asyncio.run(main())
