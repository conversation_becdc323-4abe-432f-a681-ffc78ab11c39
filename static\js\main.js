/**
 * NexusAI Hub 主JavaScript文件
 * 处理聊天和通用UI功能
 */

// 当文档加载完成时执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initPage();
    
    // 加载模型列表
    loadModels();
    
    // 初始化事件监听器
    initEventListeners();
});

/**
 * 初始化页面
 */
function initPage() {
    // 检查是否是第一次访问，如果是则显示引导遮罩层
    if (localStorage.getItem('hasVisited') !== 'true') {
        const onboardingOverlay = document.getElementById('onboardingOverlay');
        if (onboardingOverlay) {
            onboardingOverlay.classList.remove('hidden');
        }
        localStorage.setItem('hasVisited', 'true');
    }
    
    // 如果有记忆的主题设置，应用它
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.innerHTML = '<i class="fas fa-sun text-sm"></i>';
        }
    }
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 聊天输入框自动调整高度
    const userInput = document.getElementById('userInput');
    if (userInput) {
        userInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
    
    // 发送按钮点击事件
    const sendButton = document.getElementById('sendButton');
    if (sendButton) {
        sendButton.addEventListener('click', sendMessage);
    }
}

/**
 * 加载模型列表
 */
function loadModels() {
    const modelSelect = document.getElementById('selectedModel');
    if (!modelSelect) return;
    
    fetch('/api/models')
        .then(response => response.json())
        .then(data => {
            modelSelect.innerHTML = '<option value="">请选择模型</option>';

            // 检查数据类型，API返回的是对象而不是数组
            let models = [];
            if (Array.isArray(data)) {
                models = data;
            } else if (typeof data === 'object' && data !== null) {
                // 将对象转换为数组
                models = Object.entries(data).map(([name, config]) => ({
                    name: name,
                    provider: config.provider || 'unknown',
                    ...config
                }));
            }

            if (models.length === 0) {
                modelSelect.innerHTML += '<option value="" disabled>没有可用模型</option>';
                return;
            }

            models.forEach(model => {
                modelSelect.innerHTML += `<option value="${model.name}">${model.name} (${model.provider})</option>`;
            });
            
            // 如果有保存的选择，恢复它
            const savedModel = localStorage.getItem('selectedModel');
            if (savedModel) {
                modelSelect.value = savedModel;
            }
        })
        .catch(error => {
            console.error('加载模型失败:', error);
            modelSelect.innerHTML = '<option value="" disabled>加载模型失败</option>';
        });
}

/**
 * 加载MCP服务器列表
 */
function loadMCPServers() {
    const mcpContainer = document.getElementById('mcpConfigArea');
    if (!mcpContainer) return;
    
    fetch('/api/mcp-servers')
        .then(response => response.json())
        .then(data => {
            // 实现MCP服务器列表的显示逻辑
            const serverList = document.createElement('div');
            serverList.className = 'space-y-4 p-6';
            
            if (data.length === 0) {
                serverList.innerHTML = '<div class="text-center text-gray-500 p-4">没有配置MCP服务器</div>';
            } else {
                data.forEach(server => {
                    // 为每个服务器创建一个卡片
                    const card = document.createElement('div');
                    card.className = 'bg-white p-4 rounded-lg shadow border border-gray-200';
                    card.innerHTML = `
                        <div class="flex justify-between items-center mb-3">
                            <h3 class="font-semibold text-lg">${server.name}</h3>
                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">${server.connection_type}</span>
                        </div>
                        <p class="text-gray-600 text-sm mb-3">${server.description || '无描述'}</p>
                        <div class="flex justify-end space-x-2">
                            <button onclick="checkMCPServerHealth('${server.name}')" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                                健康检查
                            </button>
                            <button onclick="deleteMCPServer('${server.name}')" class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                                删除
                            </button>
                        </div>
                    `;
                    serverList.appendChild(card);
                });
            }
            
            // 添加"添加服务器"按钮
            const addButton = document.createElement('div');
            addButton.className = 'mt-4';
            addButton.innerHTML = `
                <button onclick="showAddMCPServerForm()" class="w-full py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                    <i class="fas fa-plus mr-2"></i> 添加MCP服务器
                </button>
            `;
            serverList.appendChild(addButton);
            
            // 清空容器并添加新内容
            mcpContainer.innerHTML = '';
            mcpContainer.appendChild(serverList);
        })
        .catch(error => {
            console.error('加载MCP服务器失败:', error);
            mcpContainer.innerHTML = '<div class="text-center text-red-500 p-4">加载MCP服务器失败</div>';
        });
}

/**
 * 发送消息
 */
function sendMessage() {
    const userInput = document.getElementById('userInput');
    const chatMessages = document.getElementById('chatMessages');
    const modelSelect = document.getElementById('selectedModel');
    const streamToggle = document.getElementById('streamToggle');
    
    if (!userInput || !chatMessages || !modelSelect) return;
    
    const message = userInput.value.trim();
    const selectedModel = modelSelect.value;
    
    if (!message) {
        alert('请输入消息');
        return;
    }
    
    if (!selectedModel) {
        alert('请选择一个模型');
        return;
    }
    
    // 保存用户的模型选择
    localStorage.setItem('selectedModel', selectedModel);
    
    // 显示用户消息
    const userMessageElement = document.createElement('div');
    userMessageElement.className = 'message user-message fade-in';
    userMessageElement.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="message-content">
            <div class="bg-indigo-50 p-3 rounded-lg">
                <p class="text-gray-800">${message.replace(/\n/g, '<br>')}</p>
            </div>
        </div>
    `;
    chatMessages.appendChild(userMessageElement);
    
    // 清空输入框
    userInput.value = '';
    userInput.style.height = 'auto';
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // 添加AI回复占位符
    const aiMessageElement = document.createElement('div');
    aiMessageElement.className = 'message ai-message fade-in';
    aiMessageElement.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="bg-green-50 p-3 rounded-lg">
                <p class="text-gray-800" id="ai-response-${Date.now()}">
                    <span class="inline-block w-4 h-4 bg-gray-300 rounded-full animate-pulse"></span>
                    <span class="inline-block w-4 h-4 bg-gray-300 rounded-full animate-pulse" style="animation-delay: 0.2s"></span>
                    <span class="inline-block w-4 h-4 bg-gray-300 rounded-full animate-pulse" style="animation-delay: 0.4s"></span>
                </p>
            </div>
        </div>
    `;
    chatMessages.appendChild(aiMessageElement);
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // 获取MCP服务器选择
    const mcpServers = [];
    // 这里可以实现从UI获取已选择的MCP服务器
    
    // 获取是否使用流式输出
    const stream = streamToggle.checked;
    
    // 获取API密钥（如果需要的话）
    const apiKey = '';  // 从UI或本地存储中获取
    
    // 发送请求
    if (stream) {
        // 实现流式输出的逻辑
        fetchChatStream(message, selectedModel, apiKey, mcpServers, aiMessageElement);
    } else {
        // 实现非流式输出的逻辑
        fetchChat(message, selectedModel, apiKey, mcpServers, aiMessageElement);
    }
}

/**
 * 发送非流式聊天请求
 */
function fetchChat(message, model, apiKey, mcpServers, aiMessageElement) {
    fetch('/api/chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message,
            model: model,
            api_key: apiKey,
            mcp_servers: mcpServers,
            stream: false
        })
    })
    .then(response => response.json())
    .then(data => {
        // 更新AI回复
        const responseElement = aiMessageElement.querySelector('p');
        if (responseElement) {
            responseElement.innerHTML = formatMessage(data.response);
        }
        
        // 滚动到底部
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    })
    .catch(error => {
        console.error('聊天请求失败:', error);
        const responseElement = aiMessageElement.querySelector('p');
        if (responseElement) {
            responseElement.innerHTML = '<span class="text-red-500">发生错误，请重试</span>';
        }
    });
}

/**
 * 发送流式聊天请求
 */
function fetchChatStream(message, model, apiKey, mcpServers, aiMessageElement) {
    const responseElement = aiMessageElement.querySelector('p');
    if (!responseElement) return;
    
    responseElement.innerHTML = '';
    
    // 构建查询参数
    const queryParams = new URLSearchParams({
        message: message,
        model: model,
        stream: true
    });
    
    if (apiKey) queryParams.append('api_key', apiKey);
    if (mcpServers.length > 0) queryParams.append('mcp_servers', mcpServers.join(','));
    
    // 创建EventSource
    const eventSource = new EventSource(`/api/chat?${queryParams.toString()}`);
    
    eventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.content) {
                responseElement.innerHTML += formatMessage(data.content);
                
                // 滚动到底部
                const chatMessages = document.getElementById('chatMessages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }
        } catch (e) {
            console.error('解析消息失败:', e);
        }
    };
    
    eventSource.onerror = function(error) {
        console.error('EventSource错误:', error);
        eventSource.close();
        
        if (responseElement.innerHTML === '') {
            responseElement.innerHTML = '<span class="text-red-500">发生错误，请重试</span>';
        }
    };
    
    eventSource.addEventListener('end', function(event) {
        eventSource.close();
    });
}

/**
 * 格式化消息内容，处理Markdown等格式
 */
function formatMessage(content) {
    if (!content) return '';
    
    // 替换换行符为<br>
    let formatted = content.replace(/\n/g, '<br>');
    
    // 处理代码块
    formatted = formatted.replace(/```([a-z]*)\n([\s\S]*?)```/g, function(match, language, code) {
        return `
            <div class="code-block">
                <div class="code-header">
                    <span class="code-language">${language || 'text'}</span>
                    <button class="code-copy-btn" onclick="copyCode(this)">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>
                <pre><code class="language-${language || 'text'}">${code.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
            </div>
        `;
    });
    
    // 处理内联代码
    formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
    
    // 处理粗体
    formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    
    // 处理斜体
    formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    
    return formatted;
}

/**
 * 复制代码
 */
function copyCode(button) {
    const codeBlock = button.closest('.code-block');
    const code = codeBlock.querySelector('code').innerText;
    
    navigator.clipboard.writeText(code).then(function() {
        // 临时改变按钮文本
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> 已复制';
        
        setTimeout(function() {
            button.innerHTML = originalHtml;
        }, 2000);
    }, function(err) {
        console.error('复制失败:', err);
    });
}

/**
 * 切换主题
 */
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');
    
    if (body.classList.contains('dark-mode')) {
        body.classList.remove('dark-mode');
        if (themeToggle) themeToggle.innerHTML = '<i class="fas fa-moon text-sm"></i>';
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.add('dark-mode');
        if (themeToggle) themeToggle.innerHTML = '<i class="fas fa-sun text-sm"></i>';
        localStorage.setItem('theme', 'dark');
    }
}

/**
 * 处理键盘事件
 */
function handleKeyDown(event) {
    // 如果是Enter键但没有按住Shift键，发送消息
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault(); // 阻止默认的换行行为
        sendMessage();
    }
}

/**
 * 清空聊天记录
 */
function clearChat() {
    if (!confirm('确定要清空所有聊天记录吗？')) return;
    
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        // 保留欢迎消息
        const welcomeMessage = chatMessages.querySelector('.system-message');
        chatMessages.innerHTML = '';
        if (welcomeMessage) {
            chatMessages.appendChild(welcomeMessage);
        }
    }
    
    // 清空服务器端记录
    fetch('/api/chat-history', {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            console.log('聊天记录已清空');
        }
    })
    .catch(error => {
        console.error('清空聊天记录失败:', error);
    });
}

/**
 * 清空输入框
 */
function clearInput() {
    const userInput = document.getElementById('userInput');
    if (userInput) {
        userInput.value = '';
        userInput.style.height = 'auto';
    }
}

/**
 * 关闭引导遮罩层
 */
function closeOnboarding() {
    const onboardingOverlay = document.getElementById('onboardingOverlay');
    if (onboardingOverlay) {
        onboardingOverlay.classList.add('hidden');
    }
}

/**
 * 显示系统信息
 */
function showSystemInfo() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            // 创建系统信息模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
            modal.id = 'systemInfoModal';
            
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-900">系统信息</h3>
                            <button onclick="document.getElementById('systemInfoModal').remove()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">系统状态</h4>
                                <p class="text-lg font-semibold text-gray-900">${data.status}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">版本</h4>
                                <p class="text-lg font-semibold text-gray-900">${data.version}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">运行时间</h4>
                                <p class="text-lg font-semibold text-gray-900">${data.uptime}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">API请求数</h4>
                                <p class="text-lg font-semibold text-gray-900">${data.api_requests}</p>
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500">MCP状态</h4>
                                <p class="text-lg font-semibold text-gray-900">${data.mcp_status}</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                        <button onclick="document.getElementById('systemInfoModal').remove()" class="w-full py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        })
        .catch(error => {
            console.error('获取系统信息失败:', error);
            alert('获取系统信息失败');
        });
}

/**
 * 显示设置
 */
function showSettings() {
    // 创建设置模态框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
    modal.id = 'settingsModal';
    
    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-bold text-gray-900">设置</h3>
                    <button onclick="document.getElementById('settingsModal').remove()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">界面设置</h4>
                        <label class="flex items-center space-x-3 mb-3">
                            <input type="checkbox" id="darkModeToggle" class="rounded text-blue-600 focus:ring-blue-500 h-4 w-4">
                            <span class="text-gray-700">深色模式</span>
                        </label>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">API密钥设置</h4>
                        <div class="space-y-2">
                            <label class="block text-sm text-gray-700">OpenAI API密钥</label>
                            <input type="password" id="openaiApiKey" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-gray-50 border-t border-gray-200 rounded-b-lg flex justify-end space-x-3">
                <button onclick="document.getElementById('settingsModal').remove()" class="py-2 px-4 bg-gray-200 text-gray-800 rounded hover:bg-gray-300">
                    取消
                </button>
                <button onclick="saveSettings()" class="py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700">
                    保存
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // 设置当前值
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.checked = document.body.classList.contains('dark-mode');
    }
    
    // 设置API密钥
    const openaiApiKey = document.getElementById('openaiApiKey');
    if (openaiApiKey) {
        openaiApiKey.value = localStorage.getItem('openaiApiKey') || '';
    }
}

/**
 * 保存设置
 */
function saveSettings() {
    // 保存深色模式设置
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        if (darkModeToggle.checked) {
            document.body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark');
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) themeToggle.innerHTML = '<i class="fas fa-sun text-sm"></i>';
        } else {
            document.body.classList.remove('dark-mode');
            localStorage.setItem('theme', 'light');
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) themeToggle.innerHTML = '<i class="fas fa-moon text-sm"></i>';
        }
    }
    
    // 保存API密钥
    const openaiApiKey = document.getElementById('openaiApiKey');
    if (openaiApiKey) {
        localStorage.setItem('openaiApiKey', openaiApiKey.value);
    }
    
    // 关闭模态框
    const modal = document.getElementById('settingsModal');
    if (modal) {
        modal.remove();
    }
}