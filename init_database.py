#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有必要的数据库表和初始数据
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    print("🗄️ 初始化数据库...")
    
    try:
        # 检查是否需要安装依赖
        missing_deps = []
        
        try:
            import sqlalchemy
        except ImportError:
            missing_deps.append("sqlalchemy>=2.0.9")
        
        try:
            import bcrypt
        except ImportError:
            missing_deps.append("bcrypt>=4.0.1")
        
        try:
            import jwt
        except ImportError:
            missing_deps.append("PyJWT>=2.8.0")
        
        if missing_deps:
            print("❌ 缺少必要的依赖包:")
            for dep in missing_deps:
                print(f"  - {dep}")
            print("\n请先安装依赖:")
            print("pip install " + " ".join(missing_deps))
            return False
        
        # 导入数据库模型
        from auth_system import Base, engine, auth_service
        from billing_system import Plan, Subscription, Invoice, UsageQuota, billing_service
        
        print("✅ 依赖包检查通过")
        
        # 创建数据库表
        print("📋 创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
        # 检查是否已有数据
        db = auth_service.get_db()
        try:
            plan_count = db.query(Plan).count()
            if plan_count > 0:
                print(f"✅ 数据库已有 {plan_count} 个套餐，跳过初始化")
                return True
        finally:
            db.close()
        
        # 初始化默认套餐
        print("📦 初始化默认套餐...")
        billing_service._init_default_plans()
        print("✅ 默认套餐初始化成功")
        
        # 验证数据库
        db = auth_service.get_db()
        try:
            plans = billing_service.get_plans(db)
            print(f"✅ 验证成功，共有 {len(plans)} 个套餐:")
            for plan in plans:
                print(f"  📦 {plan.name} - ¥{plan.monthly_price}/月")
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        logger.error(f"Database initialization error: {e}")
        return False

def create_sample_data():
    """创建示例数据"""
    print("\n👤 创建示例数据...")
    
    try:
        from auth_system import auth_service, UserCreate
        from billing_system import billing_service, SubscriptionCreate, BillingCycle
        
        # 创建示例用户
        print("📝 创建示例用户...")
        user_data = UserCreate(
            email="<EMAIL>",
            username="admin",
            password="admin123456",
            full_name="系统管理员"
        )
        
        db = auth_service.get_db()
        try:
            # 检查用户是否已存在
            from auth_system import User
            existing_user = db.query(User).filter(User.email == user_data.email).first()
            
            if existing_user:
                print(f"✅ 管理员用户已存在: {existing_user.email}")
                user = existing_user
            else:
                user = auth_service.create_user(user_data, db)
                print(f"✅ 管理员用户创建成功: {user.email}")
            
            # 获取用户的组织
            from auth_system import UserOrganization
            user_org = db.query(UserOrganization).filter(
                UserOrganization.user_id == user.id
            ).first()
            
            if user_org:
                # 检查是否已有订阅
                from billing_system import Subscription
                existing_sub = db.query(Subscription).filter(
                    Subscription.organization_id == user_org.organization_id,
                    Subscription.status == "active"
                ).first()
                
                if not existing_sub:
                    # 创建专业版订阅
                    plans = billing_service.get_plans(db)
                    pro_plan = next((p for p in plans if "专业" in p.name or "个人" in p.name), None)
                    
                    if pro_plan:
                        subscription_data = SubscriptionCreate(
                            plan_id=pro_plan.id,
                            billing_cycle=BillingCycle.MONTHLY
                        )
                        
                        subscription = billing_service.create_subscription(
                            user_org.organization_id, subscription_data, db
                        )
                        print(f"✅ 订阅创建成功: {subscription.plan.name}")
                    else:
                        print("⚠️ 未找到合适的套餐")
                else:
                    print(f"✅ 订阅已存在: {existing_sub.plan.name}")
            
        finally:
            db.close()
        
        print("✅ 示例数据创建完成")
        print("\n🔑 管理员账户信息:")
        print(f"  邮箱: <EMAIL>")
        print(f"  密码: admin123456")
        print("  请在生产环境中修改默认密码！")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例数据创建失败: {e}")
        logger.error(f"Sample data creation error: {e}")
        return False

def check_database_status():
    """检查数据库状态"""
    print("\n📊 检查数据库状态...")
    
    try:
        from auth_system import auth_service, User, Organization
        from billing_system import billing_service, Plan, Subscription
        
        db = auth_service.get_db()
        try:
            # 统计数据
            user_count = db.query(User).count()
            org_count = db.query(Organization).count()
            plan_count = db.query(Plan).count()
            sub_count = db.query(Subscription).count()
            
            print(f"  👥 用户数量: {user_count}")
            print(f"  🏢 组织数量: {org_count}")
            print(f"  📦 套餐数量: {plan_count}")
            print(f"  💳 订阅数量: {sub_count}")
            
            # 显示套餐信息
            if plan_count > 0:
                print("\n📦 可用套餐:")
                plans = billing_service.get_plans(db)
                for plan in plans:
                    print(f"  📋 {plan.name}")
                    print(f"     💰 价格: ¥{plan.monthly_price}/月")
                    print(f"     👥 用户: {plan.max_users}")
                    print(f"     📞 API: {plan.max_api_calls_monthly}/月")
                    print(f"     💾 存储: {plan.max_storage_gb}GB")
            
        finally:
            db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库状态检查失败: {e}")
        return False

def main():
    """主初始化流程"""
    print("🚀 IntelliHub Pro - 数据库初始化")
    print("=" * 50)
    
    # 1. 初始化数据库
    if not init_database():
        print("❌ 数据库初始化失败")
        sys.exit(1)
    
    # 2. 创建示例数据
    create_sample_data()
    
    # 3. 检查数据库状态
    check_database_status()
    
    print("\n🎉 数据库初始化完成!")
    print("\n💡 下一步:")
    print("1. 启动应用: python app.py")
    print("2. 访问主页: http://localhost:8000")
    print("3. 访问企业仪表板: http://localhost:8000/business")
    print("4. 使用管理员账户登录测试功能")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断，退出初始化")
    except Exception as e:
        print(f"\n❌ 初始化过程中发生错误: {e}")
        logger.error(f"Initialization failed: {e}")
