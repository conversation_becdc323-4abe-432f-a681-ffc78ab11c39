# Cognee AI记忆系统集成指南

## 概述

本项目已成功集成了[Cognee](https://github.com/topoteretes/cognee) - 一个为AI代理提供动态记忆的开源系统。Cognee可以在5行代码内为AI应用构建知识图谱和长期记忆能力。

## 主要功能

### 🧠 AI记忆系统
- **动态记忆构建**: 自动从对话、文档中提取和存储知识
- **知识图谱生成**: 使用ECL（Extract, Cognify, Load）管道构建知识图谱
- **智能搜索**: 基于语义的知识检索和推理
- **上下文记忆**: 维护长期对话上下文和历史记录

### 🔧 集成特性
- **无缝集成**: 与现有聊天系统完全集成
- **自动保存**: 对话自动保存到Cognee记忆中
- **多格式支持**: 支持文本、文档等多种数据格式
- **可视化界面**: 专门的Web界面管理记忆系统

## 安装和配置

### 1. 自动安装（推荐）

```bash
python install_cognee.py
```

### 2. 手动安装

```bash
pip install cognee>=0.1.42
pip install neo4j>=5.0.0
pip install chromadb>=0.4.0
pip install sentence-transformers>=2.2.0
pip install networkx>=3.0
```

### 3. 环境配置

在使用前需要配置LLM API密钥：

```bash
# 方式1: 环境变量
export LLM_API_KEY="your-openai-api-key"
export OPENAI_API_KEY="your-openai-api-key"

# 方式2: 在Web界面中配置
# 访问 http://localhost:8000/cognee 进行配置
```

## 使用方法

### 1. 访问Cognee界面

启动应用后，访问：
```
http://localhost:8000/cognee
```

### 2. 初始化服务

1. 在Cognee界面中输入API密钥
2. 选择LLM提供商（OpenAI/Anthropic）
3. 点击"初始化"按钮

### 3. 添加内容到记忆

#### 添加文本
```python
# 通过Web界面
# 在"添加文本"区域输入内容并点击"添加文本"

# 通过API
import requests
response = requests.post('http://localhost:8000/api/cognee/add-text', 
    json={
        'text': '这是要添加到记忆中的文本',
        'metadata': {'source': 'manual_input'}
    }
)
```

#### 上传文件
- 支持拖拽上传
- 支持多文件同时上传
- 支持.txt, .md, .pdf, .docx格式

### 4. 生成知识图谱

点击"生成知识图谱 (Cognify)"按钮，系统将：
1. 分析已添加的所有内容
2. 提取实体和关系
3. 构建知识图谱
4. 建立语义连接

### 5. 搜索记忆

在搜索框中输入查询，系统将：
1. 进行语义搜索
2. 返回相关内容
3. 显示相关性评分
4. 提供上下文信息

## API接口

### 状态检查
```http
GET /api/cognee/status
```

### 初始化服务
```http
POST /api/cognee/initialize
Content-Type: application/json

{
    "api_key": "your-api-key",
    "provider": "openai"
}
```

### 添加文本
```http
POST /api/cognee/add-text
Content-Type: application/json

{
    "text": "要添加的文本内容",
    "metadata": {
        "source": "api",
        "timestamp": "2024-01-01T00:00:00Z"
    }
}
```

### 搜索记忆
```http
POST /api/cognee/search
Content-Type: application/json

{
    "query": "搜索查询",
    "limit": 5
}
```

### 生成知识图谱
```http
POST /api/cognee/cognify
```

## 自动集成功能

### 对话自动保存
系统会自动将所有对话保存到Cognee记忆中：
- 用户消息和AI回复
- 会话上下文信息
- 时间戳和元数据

### 智能上下文
在聊天时，系统可以：
- 引用历史对话内容
- 基于记忆提供更准确的回答
- 维护长期的对话上下文

## 数据存储

### 本地存储
- 数据目录: `./cognee_data/`
- 向量数据库: ChromaDB
- 图数据库: Neo4j（可选）

### 数据安全
- 所有数据本地存储
- 不会上传到外部服务
- 支持数据备份和恢复

## 故障排除

### 常见问题

1. **Cognee导入失败**
   ```bash
   # 重新安装
   pip uninstall cognee
   pip install cognee>=0.1.42
   ```

2. **API密钥错误**
   - 检查密钥格式是否正确
   - 确认密钥有效期
   - 验证网络连接

3. **内存不足**
   - 减少批处理大小
   - 清理旧的记忆数据
   - 增加系统内存

4. **搜索结果为空**
   - 确认已执行cognify操作
   - 检查搜索词是否准确
   - 验证数据是否正确添加

### 日志查看
```bash
# 查看应用日志
tail -f app.log

# 查看Cognee服务日志
tail -f cognee_data/logs/cognee.log
```

## 高级配置

### 自定义数据目录
```python
# 在cognee_service.py中修改
cognee_service = CogneeService(data_dir="./custom_cognee_data")
```

### 配置向量数据库
```python
# 自定义ChromaDB配置
import chromadb
client = chromadb.Client(Settings(
    chroma_db_impl="duckdb+parquet",
    persist_directory="./custom_chroma_db"
))
```

## 性能优化

### 建议配置
- **内存**: 最少4GB，推荐8GB+
- **存储**: SSD硬盘，至少10GB可用空间
- **网络**: 稳定的互联网连接（用于LLM API调用）

### 批处理优化
- 批量添加文档时分批处理
- 定期执行cognify操作
- 合理设置搜索结果数量限制

## 更新和维护

### 更新Cognee
```bash
pip install --upgrade cognee
```

### 数据备份
```bash
# 备份整个数据目录
cp -r ./cognee_data ./cognee_data_backup_$(date +%Y%m%d)
```

### 清理数据
通过Web界面的"重置记忆数据"功能，或：
```bash
rm -rf ./cognee_data
mkdir ./cognee_data
```

## 支持和反馈

- **Cognee项目**: https://github.com/topoteretes/cognee
- **文档**: https://docs.cognee.ai
- **社区**: https://discord.gg/NQPKmU5CCg

## 许可证

本集成遵循原项目的Apache-2.0许可证。
